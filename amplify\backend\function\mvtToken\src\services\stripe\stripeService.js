let contractService = require('../blockchain/contractService');
let axios = require('axios'); // Add axios for direct HTTP calls

// Initialize Stripe with API key from environment variables
let stripe;
try {
  if (!process.env.STRIPE_SECRET_KEY) {
    console.error("STRIPE_SECRET_KEY environment variable is not set");
    // Initialize without a key for now, will fail gracefully when called
    stripe = require('stripe')();
  } else {
    console.log("Initializing Stripe with API key");
    // Specify the API version that supports Crypto Onramp
    stripe = require('stripe')(process.env.STRIPE_SECRET_KEY, {
      apiVersion: '2023-10-16', // Use the API version that supports Crypto Onramp
      appInfo: {
        name: 'MyVillage Token App',
        version: '1.0.0'
      }
    });
  }
} catch (error) {
  console.error("Error initializing Stripe:", error);
  // Initialize without a key for now, will fail gracefully when called
  stripe = require('stripe')();
}

/**
 * Creates a Stripe Crypto Onramp session for purchasing USDC
 *
 * @param {string} usdcAmount - The amount of USDC to purchase
 * @param {string} userWallet - User's blockchain wallet address
 * @returns {Promise<{id: string, client_secret: string}>} - Onramp session details
 */

function createOnrampSession(usdcAmount, userWallet) {
  return new Promise(function(resolve, reject) {
    try {
      console.log("Creating Stripe Crypto Onramp session for " + usdcAmount + " USDC to wallet " + userWallet);

      if (!process.env.STRIPE_SECRET_KEY) {
        throw new Error('STRIPE_SECRET_KEY environment variable is not set. Please configure your API key.');
      }

      if (!usdcAmount || isNaN(parseFloat(usdcAmount)) || parseFloat(usdcAmount) <= 0) {
        throw new Error('Invalid MVT amount provided');
      }

      if (!userWallet?.startsWith('0x')) {
        throw new Error('Invalid wallet address provided');
      }

      const withdrawContractAddress = process.env.MVT_WITHDRAW_CONTRACT_ADDRESS;
      
      if (!withdrawContractAddress) {
        throw new Error('MVT_USDC_CONTRACT_ADDRESS environment variable is not set');
      }
      
      if (process.env.STRIPE_SECRET_KEY) {
        const apiKeyFirstFour = process.env.STRIPE_SECRET_KEY.substring(0, 4);
        const apiKeyLength = process.env.STRIPE_SECRET_KEY.length;
        console.log("Using Stripe API key starting with " + apiKeyFirstFour + "... (" + apiKeyLength + " characters long)");
      } else {
        console.log("STRIPE_SECRET_KEY is not set");
      }
      
      let isTestMode = process.env.NODE_ENV !== 'production' || process.env.STRIPE_SECRET_KEY.startsWith('sk_test_');
      console.log("Operating in " + (isTestMode ? "TEST" : "PRODUCTION") + " mode");

      let sessionParams = {
        'destination_currencies[]': 'usdc',
        'destination_currency': 'usdc',
        'destination_networks[]': 'ethereum',
        'destination_network': 'ethereum',
        'wallet_addresses[ethereum]': withdrawContractAddress,
        'lock_wallet_address': true,
        'source_currency': 'usd',
        'source_amount': parseFloat(usdcAmount),
        'metadata[user_wallet]': userWallet,
        'metadata[transaction_type]': 'usdc_to_mvt',
        'metadata[mvt_conversion_ratio]': '1',
      };
      
      console.log('sessionParams: ', sessionParams);

      if (isTestMode) {
        console.log("Adding test parameters to simplify KYC process");
      }
      
      axios.post(
        'https://api.stripe.com/v1/crypto/onramp_sessions',
        new URLSearchParams(sessionParams),
        {
          headers: {
            'Authorization': 'Bearer ' + process.env.STRIPE_SECRET_KEY,
            'Content-Type': 'application/x-www-form-urlencoded'
          }
        }
      ).then(function(response) {
        const session = response.data;
        console.log("Stripe Onramp session created successfully:", session.id);
        console.log("Session livemode:", session.livemode ? "LIVE" : "TEST");
        
        if (session.redirect_url) {
          console.log("Redirect URL for testing:", session.redirect_url);
        }
        
        if (!session.livemode || isTestMode) {
          console.log("TEST MODE ACTIVE: Use these test credentials for KYC:");
          console.log("- Email: any test email");
          console.log("- Phone: +18004444444");
          console.log("- OTP Code: 000000");
          console.log("- Name: John Verified");
          console.log("- Birthday: January 1, 1901");
          console.log("- SSN: 000000000");
          console.log("- Address: address_full_match, Seattle, WA 12345");
          console.log("- Test Card: ****************, exp: 12/24, cvc: 123");
        }
        
        resolve({
          id: session.id,
          client_secret: session.client_secret,
          livemode: session.livemode
        });
      }).catch(function(error) {
        console.error('Error creating Stripe Onramp session:', error.response ? error.response.data : error.message);
        reject(new Error("Failed to create onramp session: " + (error.response?.data?.error?.message || error.message)));
      });
    } catch (error) {
      console.error('Error creating Stripe Crypto Onramp session:', error);
      reject(new Error("Failed to create onramp session: " + error.message));
    }
  });
}

/**
 * Handles Stripe webhook events for onramp sessions
 * 
 * @param {Object} event - Stripe webhook event object
 * @returns {Promise<Object>} - Result of webhook processing
 */
function handleWebhook(event) {
  return new Promise(function(resolve, reject) {
    try {
      console.log("Processing Stripe webhook event: " + event.type);
      console.log("Event ID: " + event.id);
      console.log("Event Created: " + new Date(event.created * 1000).toISOString());
      console.log("Event livemode:", event.livemode ? "LIVE" : "TEST");
      
      // Handle different event types
      switch (event.type) {
        case 'crypto.onramp.session.completed': {
          const session = event.data.object;
          console.log("Crypto Onramp Session Completed:");
          console.log("- Session ID: " + session.id);
          console.log("- Status: " + session.status);

          // Access transaction details
          const txDetails = session.transaction_details;
          const metadata = session.metadata || {};
          
          if (metadata) {
            console.log("- User Wallet: " + (metadata.user_wallet || "undefined"));
            console.log("- Transaction Type: " + (metadata.transaction_type || "undefined"));
          }

          // Check if this is a USDC to MVT transaction
          if (metadata && metadata.transaction_type === 'usdc_to_mvt') {
            console.log("This is a USDC to MVT purchase, processing token transfer...");

            let userWallet = metadata.user_wallet;
            const mvtConversionRatio = parseFloat(metadata.mvt_conversion_ratio || "1");
            const usdcAmount = txDetails.destination_exchange_amount;
            const mvtAmount = usdcAmount * mvtConversionRatio;

            if (!userWallet || !usdcAmount) {
              reject(new Error("Missing required data in webhook: user wallet or USDC amount"));
              return;
            }

            console.log("Processing MVT token transfer: " + mvtAmount + " MVT to " + userWallet);

            contractService.transferMVT(userWallet, mvtAmount.toString())
              .then(function(transferResult) {
                console.log("MVT transfer successful:", transferResult);

                resolve({
                  success: true,
                  message: "Successfully processed onramp session and transferred MVT tokens",
                  data: {
                    sessionId: session.id,
                    usdcAmount: usdcAmount,
                    mvtAmount: mvtAmount,
                    userWallet: userWallet,
                    transactionHash: transferResult?.hash ?? null
                  }
                });
              })
              .catch(function(error) {
                console.error("Error processing token transfer:", error);
                reject(new Error("Failed to process token transfer: " + error.message));
              });
          } else {
            console.log('Not a USDC to MVT transaction, ignoring');
            resolve({ 
              success: true, 
              message: 'Event ignored (not a USDC to MVT transaction)' 
            });
          }
          break;
        }
          
        case 'crypto.onramp.session.created':
          console.log("Crypto Onramp Session Created");
          resolve({ success: true, message: 'Session created event acknowledged' });
          break;
          
        case 'crypto.onramp.session.expired':
          console.log("Crypto Onramp Session Expired");
          resolve({ success: true, message: 'Session expired event acknowledged' });
          break;
          
        default:
          console.log("Unhandled event type: " + event.type);
          resolve({ success: true, message: 'Unhandled event type' });
      }
    } catch (error) {
      console.error('Error processing webhook:', error);
      reject(new Error(error.message || 'An unknown error occurred'));
    }
  });
}

module.exports = {
  createOnrampSession: createOnrampSession,
  handleWebhook: handleWebhook
};