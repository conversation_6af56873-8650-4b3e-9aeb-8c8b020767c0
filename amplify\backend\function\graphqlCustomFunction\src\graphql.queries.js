const { gql } = require("@apollo/client/core");

module.exports = {
  GET_MEMBER_ASSIGNMENTS: gql`
    query GetMemberAssignments(
      $filterUser: ModelHomeworkUsersFilterInput
      $filterOrg: ModelHomeworkOrganizationsFilterInput
    ) {
      listHomeworkUsers(filter: $filterUser, limit: 100000) {
        items {
          homeworkStatus
          homeworkData {
            id
            name
            imageUrl
            dueDate
            assignmentPoints
            assignmentType
            shortDescription
            longDescription
            coCreationType
            cityId
            homeworkAutoGeneratedFrom
            createdBy
            createdAt
            isDeleted
          }
        }
      }
      listHomeworkOrganizations(filter: $filterOrg, limit: 100000) {
        items {
          homeworkStatus
          homeworkData {
            id
            name
            imageUrl
            dueDate
            assignmentPoints
            assignmentType
            shortDescription
            longDescription
            coCreationType
            cityId
            homeworkAutoGeneratedFrom
            createdBy
            createdAt
            isDeleted
          }
        }
      }
    }
  `,
  GET_ASSOCIATED_DATA: gql`
    query GetAssociatedData($filter: ModelAssociationFilterInput) {
      associationsByDate(filter: $filter, limit: 100000, isDeleted: "false") {
        items {
          id
          relationType
          type
          person {
            id
            name
            isStakeholder
          }
          otherPerson {
            id
            name
            isStakeholder
          }
          organization {
            id
            name
          }
        }
      }
    }
  `,
  UPDATE_USER: gql`
    mutation UpdateUser($input: UpdateUserInput!) {
      updateUser(input: $input) {
        id
        givenName
        familyName
        ethnicity
        gender
        birthday
        streetAddressOne
        streetAddressTwo
        city
        state
        zipCode
        type
        _version
      }
    }
  `,
  GET_USER: gql`
    query GetUser($id: ID!) {
      getUser(id: $id) {
        id
        givenName
        familyName
        membership {
          id
          name
          isDeleted
          _version
          _deleted
        }
        homeworks(limit: 100000) {
          items {
            id
            _version
          }
          nextToken
          startedAt
        }
        associations(limit: 100000) {
          items {
            id
            _version
          }
          nextToken
          startedAt
        }
        userAssociations(limit: 100000) {
          items {
            id
            _version
          }
          nextToken
          startedAt
        }
        _version
      }
    }
  `,
  UPDATE_MEMBERSHIP: gql`
    mutation UpdateMembership($input: UpdateMembershipInput!) {
      updateMembership(input: $input) {
        id
        name
        isDeleted
        _version
      }
    }
  `,
  DELETE_MEMBERSHIP: gql`
    mutation DeleteMembership($input: DeleteMembershipInput!) {
      deleteMembership(input: $input) {
        id
      }
    }
  `,
  DELETE_USER: gql`
    mutation DeleteUser($input: DeleteUserInput!) {
      deleteUser(input: $input) {
        id
      }
    }
  `,
  GET_USER_ASSIGNMENTS: gql`
    query GetUserAssignments($filter: ModelHomeworkUsersFilterInput) {
      listHomeworkUsers(filter: $filter, limit: 100000) {
        items {
          homeworkStatus
          homeworkData {
            id
            name
            imageUrl
            dueDate
            assignmentPoints
            assignmentType
            shortDescription
            longDescription
            coCreationType
            cityId
            createdAt
            _version
            _deleted
            microcredential {
              id
              name
              type
            }
          }
          _deleted
          studentStakeholderData {
            imageUrl
            id
            name
          }
        }
      }
    }
  `,
  GET_USER_SUBMISSIONS: gql`
    query SubmissionByDate($filter: ModelSubmissionFilterInput) {
      SubmissionByDate(isDeleted: "false", filter: $filter, limit: 100000) {
        items {
          id
          text
          description
          videos
          images
          projectType
          memberId
          cityId
          isPublic
          isActive
          createdAt
          homework {
            assignmentPoints
          }
        }
        nextToken
        startedAt
      }
    }
  `,
  GET_MEMBERSHIP_LIST: gql`
    query MembershipByDate($filter: ModelMembershipFilterInput) {
      membershipByDate(isDeleted: "false", filter: $filter, limit: 100000) {
        items {
          id
          organizationID
          personsID
          currentImpactScore
          MVPTokens
          fundTokens
        }
      }
    }
  `,
  GET_EVENTS_LIST: gql`
    query EventsByDate($filter: ModelEventsFilterInput) {
      eventsByDate(isDeleted: "false", filter: $filter, limit: 100000) {
        items {
          id
          name
          shortDescription
          longDescription
          type
          startDateTime
          endDateTime
          cityId
          structure
          streetAddress1
          streetAddress2
          city
          state
          zipcode
          EventsOrganizations(limit: 100000) {
            items {
              organizationsID
              eventsID
              organizations {
                id
                name
              }
            }
            nextToken
            startedAt
          }
          projectImage
          isDeleted
          createdAt
          _version
        }
        nextToken
        startedAt
      }
    }
  `,
  GET_HOMEWORK: gql`
    query GetHomework($id: ID!) {
      getHomework(id: $id) {
        id
        name
        shortDescription
        longDescription
        dueDate
        imageUrl
        coCreationType
        assignmentType
        microcredentialId
        microcredential {
          id
          name
          imageUrl
          totalPoints
          receivedPoints
          remainingPoints
        }
        entityType
        members(limit: 100000) {
          items {
            homeworkId
            memberId
            memberData {
              id
              imageUrl
            }
            homeworkStatus
          }
        }
        studentsStakeholders(limit: 100000) {
          items {
            homeworkId
            studentStakeholderId
            studentStakeholderData {
              id
              imageUrl
            }
            homeworkStatus
          }
        }
        assignmentPoints
        primaryContact
        isCompleted
        cityId
        createdBy
        completeCount
        selfAssignment
        maxAssignment
        isDeleted
        createdAt
        updatedAt
        _version
      }
    }
  `,
  GET_HOMEWORK_MOBILE: gql`
    query GetHomework($id: ID!) {
      getHomework(id: $id) {
        id
        name
        shortDescription
        longDescription
        dueDate
        imageUrl
        assignmentPoints
        isCompleted
        cityId
        selfAssignment
        createdAt
      }
    }
  `,

  GET_LIST_HOMEWORK: gql`
    query HomeworkByDate(
      $isDeleted: String!
      $createdAt: ModelStringKeyConditionInput
      $sortDirection: ModelSortDirection
      $filter: ModelHomeworkFilterInput
    ) {
      homeworkByDate(
        isDeleted: $isDeleted
        createdAt: $createdAt
        sortDirection: $sortDirection
        filter: $filter
      ) {
        items {
          cityId
          dueDate
          id
          imageUrl
          name
          assignmentPoints
          createdAt
          microcredential {
            gender
          }
        }
      }
    }
  `,
  CREATE_HOMEWORK_ORGANIZATIONS: gql`
    mutation CreateHomeworkOrganizations(
      $input: CreateHomeworkOrganizationsInput!
      $condition: ModelHomeworkOrganizationsConditionInput
    ) {
      createHomeworkOrganizations(input: $input, condition: $condition) {
        id
        homeworkId
        memberId
        homeworkStatus
        homeworkAssignDate
        homeworkCompletedDate
        createdAt
        updatedAt
        _version
        _deleted
        _lastChangedAt
        organizationsHomeworksId
        programsHomeworkOrganizationsId
        homeworkMembersId
      }
    }
  `,
  CREATE_HOMEWORK_USERS: gql`
    mutation CreateHomeworkUsers(
      $input: CreateHomeworkUsersInput!
      $condition: ModelHomeworkUsersConditionInput
    ) {
      createHomeworkUsers(input: $input, condition: $condition) {
        id
        homeworkId
        studentStakeholderId
        homeworkStatus
        homeworkAssignDate
        homeworkCompletedDate
        createdAt
        updatedAt
        _version
        _deleted
        _lastChangedAt
        userHomeworksId
        programsHomeworkUsersId
        homeworkStudentsStakeholdersId
      }
    }
  `,
  GET_MICROCREDENTIAL: gql`
    query GetPrograms($id: ID!) {
      getPrograms(id: $id) {
        id
        name
        homeworkOrganizations(limit: 100000) {
          items {
            id
            homeworkId
            homeworkData {
              assignmentPoints
            }
            memberId
            homeworkStatus
          }
        }
        homeworkUsers(limit: 100000) {
          items {
            id
            homeworkId
            homeworkData {
              assignmentPoints
            }
            studentStakeholderId
            homeworkStatus
          }
        }
      }
    }
  `,
  GET_MEMBER: gql`
    query GetOrganizations($id: ID!) {
      getOrganizations(id: $id) {
        id
      }
    }
  `,

  GET_HOMEWORK_USERS: gql`
    query ListHomeworkUsers(
      $filter: ModelHomeworkUsersFilterInput
      $limit: Int
      $nextToken: String
    ) {
      listHomeworkUsers(filter: $filter, limit: $limit, nextToken: $nextToken) {
        items {
          id
          homeworkId
          homeworkData {
            id
            name
            shortDescription
            longDescription
            dueDate
            imageUrl
            coCreationType
            categoryType
            assignmentType
            microcredentialId
            microcredential {
              id
              name
              shortDescription
              longDescription
              type
              status
              memberType
              cityId
              gender
              minAge
              maxAge
              imageUrl
              totalPoints
              createdBy
              receivedPoints
              remainingPoints
              homeworkAssignDate
              completedHomeworkCount
              microCredentialCode
              isDeleted
              createdAt
              updatedAt
              _version
              _deleted
              _lastChangedAt
            }
            entityType
            members {
              nextToken
              startedAt
            }
            studentsStakeholders {
              nextToken
              startedAt
            }
            assignmentPoints
            primaryContact
            primaryContactDetail {
              id
              email
              familyName
              givenName
              name
              phoneNumber
              cityId
              stackholderCities
              imageUrl
              streetAddressOne
              streetAddressTwo
              city
              state
              zipCode
              countryCode
              role
              birthday
              ethnicity
              gender
              profileUrl
              type
              impactScore
              status
              registeredFrom
              isAssociated
              userType
              deviceId
              FCMToken
              endpointArn
              loginPlatform
              isLogin
              otherDeviceId
              otherFCMToken
              isLoginOther
              isStakeholder
              defaultMessageDate
              membershipId
              customerId
              memberCode
              verificationCode
              codeExpiration
              phoneNumberVerified
              studentEnrollmentDate
              socketConnection
              logoutTime
              isDeleted
              createdAt
              updatedAt
              _version
              _deleted
              _lastChangedAt
            }
            isCompleted
            cityId
            createdBy
            associationId
            association
            completeCount
            selfAssignment
            maxAssignment
            assignmentInstruction
            user {
              id
              email
              familyName
              givenName
              name
              phoneNumber
              cityId
              stackholderCities
              imageUrl
              streetAddressOne
              streetAddressTwo
              city
              state
              zipCode
              countryCode
              role
              birthday
              ethnicity
              gender
              profileUrl
              type
              impactScore
              status
              registeredFrom
              isAssociated
              userType
              deviceId
              FCMToken
              endpointArn
              loginPlatform
              isLogin
              otherDeviceId
              otherFCMToken
              isLoginOther
              isStakeholder
              defaultMessageDate
              membershipId
              customerId
              memberCode
              verificationCode
              codeExpiration
              phoneNumberVerified
              studentEnrollmentDate
              socketConnection
              logoutTime
              isDeleted
              createdAt
              updatedAt
              _version
              _deleted
              _lastChangedAt
            }
            isDeleted
            createdAt
            updatedAt
            _version
            _deleted
            _lastChangedAt
            programsHomeworkId
          }
          studentStakeholderId
          studentStakeholderData {
            id
            email
            familyName
            givenName
            name
            phoneNumber
            cityId
            stackholderCities
            cityData {
              id
              name
              code
              isDeleted
              createdAt
              updatedAt
              _version
              _deleted
              _lastChangedAt
            }
            imageUrl
            streetAddressOne
            streetAddressTwo
            city
            state
            zipCode
            countryCode
            role
            birthday
            ethnicity
            gender
            profileUrl
            type
            impactScore
            status
            registeredFrom
            isAssociated
            userType
            deviceId
            FCMToken
            endpointArn
            loginPlatform
            isLogin
            otherDeviceId
            otherFCMToken
            isLoginOther
            isStakeholder
            defaultMessageDate
            tasks {
              nextToken
              startedAt
            }
            homeworks {
              nextToken
              startedAt
            }
            associations {
              nextToken
              startedAt
            }
            userAssociations {
              nextToken
              startedAt
            }
            post {
              nextToken
              startedAt
            }
            membershipId
            membership {
              id
              type
              name
              shortDescription
              imageUrl
              personsID
              organizationID
              businessID
              isActive
              lastAddedImpactScore
              currentImpactScore
              MVPTokens
              fundTokens
              cityId
              memberCode
              isDeleted
              createdAt
              updatedAt
              _version
              _deleted
              _lastChangedAt
            }
            customerId
            subscriptions {
              nextToken
              startedAt
            }
            userMessages {
              nextToken
              startedAt
            }
            memberCode
            verificationCode
            codeExpiration
            phoneNumberVerified
            studentEnrollmentDate
            socketConnection
            logoutTime
            isDeleted
            createdAt
            updatedAt
            _version
            _deleted
            _lastChangedAt
          }
          homeworkStatus
          homeworkAssignDate
          homeworkCompletedDate
          createdAt
          updatedAt
          _version
          _deleted
          _lastChangedAt
          userHomeworksId
          programsHomeworkUsersId
          homeworkStudentsStakeholdersId
        }
        nextToken
        startedAt
      }
    }
  `,
  GET_MICROCREDENTIALS: gql`
    query ListPrograms($filter: ModelProgramsFilterInput) {
      listPrograms(filter: $filter) {
        items {
          id
          name
          type
        }
      }
    }
  `,
  GET_USER_FOR_COMMUNITY: gql`
    query GetUser($id: ID!) {
      getUser(id: $id) {
        id
        givenName
        familyName
        userAssociations(
          filter: { isDeleted: { eq: "false" }, status: { eq: true } }
        ) {
          items {
            type
            createdAt
            organization {
              id
              name
              imageUrl
              cityId
              membership {
                currentImpactScore
                MVPTokens
                fundTokens
                id
              }
            }
          }
        }
      }
    }
  `,
  GET_USER_FOR_FAMILY: gql`
    query GetUser($id: ID!) {
      getUser(id: $id) {
        id
        givenName
        familyName
        associations(limit: 100000) {
          items {
            id
          }
        }
        userAssociations(filter: {type: {eq: "Person"}, status: {eq: true}}) {
          items {
            cityId
            id
            relationType
            type
            otherPerson {
              givenName
              familyName
              gender
              id
            }
          }
        }
      }
    }
  `,
  GET_USER_FOR_KNOWLEDGE: gql`
    query SubmissionByDate($filter: ModelSubmissionFilterInput) {
      SubmissionByDate(isDeleted: "false", filter: $filter, limit: 100000) {
        items {
          id
          text
          submissionStatus  # Approved, In-review, Denied
          createdAt         # Last submitted knowledge
          projectType       # Type of knowledge submitted
          memberId
          organization {
            organizationUser {
              name          # Submission of organizations
            }
          }
          homework {
            microcredential {
              categoryData {
                name        # Category-wise count
              }
            }
          }
        }
      }
    }
  `,
  GET_USER_DETAILS_FOR_KNOWLEDGE: gql`
    query GetUserDetailsForKnowledge($id: ID!) {
      getUser(id: $id) {
        personId: id
        associatedOrganizationIds: userAssociations(
          filter: {
            isDeleted: { eq: "false" },
            status: { eq: true },
            organizationID: { ne: null }
          }
        ) {
          items {
            organizationId: organizationID
          }
        }
        associatedPersonIds: userAssociations(
          filter: {
            isDeleted: { eq: "false" },
            status: { eq: true },
            otherPersonsID: { ne: null }
          }
        ) {
          items {
            associatedPersonId: otherPersonsID
          }
        }
      }
    }
  `,
  GET_KNOWLEDGE_REPOSITORY_STORE: gql`
  query ListKnowledgeRepositoryStores(
    $filter: ModelKnowledgeRepositoryStoreFilterInput
    $limit: Int
    $nextToken: String
  ) {
    listKnowledgeRepositoryStores(filter: $filter, limit: $limit, nextToken: $nextToken) {
      items {
        id
        name
        transcriptDetail {
          transcription
          summary
          metadata
        }
        fileType          # Type of knowledge
        durationInMinutes
        createdAt         # Last submitted knowledge
        userId
        organizationId
      }
    }
  }
  `,
  UPDATE_KNOWLEDGE_REPOSITORY_STORE: gql`
    mutation UpdateKnowledgeRepositoryStore(
      $input: UpdateKnowledgeRepositoryStoreInput!
      $condition: ModelKnowledgeRepositoryStoreConditionInput
    ) {
      updateKnowledgeRepositoryStore(input: $input, condition: $condition) {
        id
        userId
        categoryId
        subCategoryIds
        fileType
        name
        description
        durationInMinutes
        fileUrl
        status
        transcriptDetail {
          id
          jobName
          accountId
          status
          results
          metadata
          createdAt
          updatedAt
        }
        createdAt
        updatedAt
        _deleted
        _version
        _lastChangedAt
        isDeleted
      }
    }
  `,
  UPDATE_SUBMISSION: gql`
    mutation UpdateSubmission(
      $input: UpdateSubmissionInput!
      $condition: ModelSubmissionConditionInput
    ) {
      updateSubmission(input: $input, condition: $condition) {
        id
        text
        description
        videos
        images
        videosThumbnail
        pointsAssigned
        viewCount
        organizationsPostId
        businessPostId
        projectId
        memberId
        cityId
        categoryType
        submissionStatus
        isPublic
        createdBy
        gradedBy
        feedback
        transcriptDetail {
          transcription
          summary
          metadata
        }
        user {
          id
          email
          familyName
          givenName
          name
          phoneNumber
          streetAddressOne
          city
          state
          zipCode
          countryCode
          isDeleted
          createdAt
          updatedAt
          _version
          _deleted
          _lastChangedAt
        }
      }
    }
  `,
   CREATE_TRANSCRIPT_DETAIL: gql`
    mutation CreateTranscriptDetail($input: CreateTranscriptDetailInput!) {
      createTranscriptDetail(input: $input) {
        id
      }
    }
  `,
  GET_USER_FOR_UNIFY: gql`
    query GetUserForUnify($id: ID!) {
      getUser(id: $id) {
        id
        givenName
        familyName
        # Get all associations for both family and community use cases
        associations(limit: 100000) {
          items {
            id
          }
        }
        # Get all user associations with a filter that works for both use cases
        userAssociations(filter: {isDeleted: {eq: "false"}, status: {eq: true}}) {
          items {
            type
            relationType
            cityId
            id
            createdAt
            # Include organization data for community-related queries
            organization {
              id
              name
              imageUrl
              cityId
              membership {
                currentImpactScore
                MVPTokens
                fundTokens
                id
              }
            }
            # Include person data for family-related queries
            otherPerson {
              givenName
              familyName
              gender
              id
            }
          }
        }
      }
    }
  `
};