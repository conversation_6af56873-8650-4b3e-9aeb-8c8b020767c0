/**
 * Wallet-specific validation functions
 */

/**
 * Validate balance request input
 * @param {object} input - Input object
 * @returns {object} - Validation result
 */
function validateBalanceRequest(input) {
  if (input && input.userId !== undefined) {
    if (input.userId === null) {
      return { isValid: false, error: 'User ID must be a string' };
    }

    if (typeof input.userId !== 'string') {
      return { isValid: false, error: 'User ID must be a string' };
    }

    if (input.userId.trim().length === 0) {
      return { isValid: false, error: 'User ID cannot be empty' };
    }
  }

  return { isValid: true };
}

/**
 * Validate user ID parameter
 * @param {string} userId - User ID
 * @returns {object} - Validation result
 */
function validateUserId(userId) {
  if (!userId) {
    return { isValid: false, error: 'User ID is required' };
  }

  if (typeof userId !== 'string') {
    return { isValid: false, error: 'User ID must be a string' };
  }

  if (userId.trim().length === 0) {
    return { isValid: false, error: 'User ID is required' };
  }

  if (userId.length > 255) {
    return { isValid: false, error: 'User ID is too long (max 255 characters)' };
  }

  return { isValid: true };
}

/**
 * Validate amount for token operations
 * @param {number} amount - Amount to validate
 * @param {string} operation - Operation name for error messages
 * @returns {object} - Validation result
 */
function validateTokenAmount(amount, operation = 'operation') {
  if (amount === undefined || amount === null) {
    return { isValid: false, error: `Amount is required for ${operation}` };
  }

  if (typeof amount !== 'number') {
    return { isValid: false, error: `Amount must be a number for ${operation}` };
  }

  // Check for NaN and Infinity first
  if (isNaN(amount) || !isFinite(amount)) {
    return { isValid: false, error: `Amount must be a number for ${operation}` };
  }

  if (amount <= 0) {
    return { isValid: false, error: `Amount must be greater than 0 for ${operation}` };
  }

  if (!Number.isInteger(amount)) {
    return { isValid: false, error: `Amount must be an integer for ${operation}` };
  }

  if (amount > Number.MAX_SAFE_INTEGER) {
    return { isValid: false, error: `Amount is too large for ${operation}` };
  }

  return { isValid: true };
}

module.exports = {
  validateBalanceRequest,
  validateUserId,
  validateTokenAmount
};
