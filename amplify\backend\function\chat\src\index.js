/* Amplify Params - DO NOT EDIT
	API_MYVILLAGEPROJECTADMI_GRAPHQLAPIENDPOINTOUTPUT
	API_MYVILLAGEPROJECTADMI_GRAPHQLAPIIDOUTPUT
	AUTH_MYVILLAGEPROJECTADMIFEB4EA87_USERPOOLID
	ENV
	REGION
Amplify Params - DO NOT EDIT */

/**
 * @type {import('@types/aws-lambda').APIGatewayProxyHandler}
 */

const AWS = require("aws-sdk");
const uuid = require("uuid");
const {
  ApiGatewayManagementApiClient,
  PostToConnectionCommand,
} = require("@aws-sdk/client-apigatewaymanagementapi");
AWS.config.update({
  maxRetries: 3,
  httpOptions: { timeout: 30000, connectTimeout: 5000 },
  region: process.env.REGION,
  accessKeyId: process.env.ACCESS_KEY_ID,
  secretAccessKey: process.env.SECRET_ACCESS_KEY,
});
const ddb = new AWS.DynamoDB();
const handler = async (event, context, callback) => {
  try {
    const {
      body,
      requestContext: { routeKey, connectionId, domainName, stage },
      queryStringParameters = {},
    } = event;
    const ENDPOINT = `https://${domainName}/${stage}`;
    console.log("ENDPOINT", ENDPOINT);
    // const client = new AWS.ApiGatewayManagementApi({ endpoint: ENDPOINT });

    // console.log("client", client)
    console.log("event", event);
    console.log("routeKey", routeKey);

    switch (routeKey) {
      case "$connect":
        console.log("in connect");
        console.log("connectionId", connectionId);
        const { givenName, userId } = queryStringParameters;
        await onConnect(connectionId, givenName, userId);
        callback(null, {
          statusCode: 200,
          body: "Connected",
        });
        break;
      case "$disconnect":
        console.log("$disconnect");
        console.log("connectionId", connectionId);
        await onDisconnect(connectionId);
        callback(null, {
          statusCode: 200,
          body: "disconnected successfully",
        });
        break;
      case "sendMessage":
        console.log("sendMessage");
        console.log("connectionId", connectionId);
        const message = await sendMessage(connectionId, body, ENDPOINT);
        callback(null, {
          statusCode: 200,
          body: "sendMessage successfully",
        });
        break;
      case "getAllMessages":
        console.log("getAllMessages");
        console.log("connectionId", connectionId);
        console.log("body", body);
        await getAllMessages(connectionId, body, ENDPOINT);
        callback(null, {
          statusCode: 200,
          body: "getAllMessages successfully",
        });
        break;

      case "getAllChannels":
        console.log("getAllChannels");
        console.log("connectionId", connectionId);
        await getAllChannels(connectionId, ENDPOINT);
        callback(null, {
          statusCode: 200,
          body: "getAllChannels successfully",
        });
        break;
      case "clearChat":
        console.log("clearChat");
        console.log("connectionId", connectionId);
        const deleteChat = await clearMessage(connectionId, body, ENDPOINT);
      default:
        console.log("in default");
        break;
    }

    const response = {
      statusCode: 200,
      body: JSON.stringify("success"),
    };
    return response;
  } catch (error) {
    const err = createErrorResponse(error);
    console.log("error", err);
    callback(err);
  }
};

function createErrorResponse(error) {
  return {
    isSuccess: false,
    statusCode: error.statusCode ? error.statusCode : 400,
    message: error.message,
  };
}

const onConnect = async (connectionId, givenName, userId) => {
  try {
    console.log("method connectionId ", connectionId);
    console.log("here in onConnect");
    console.log(
      "table",
      "User-" +
        process.env.API_MYVILLAGEPROJECTADMI_GRAPHQLAPIIDOUTPUT +
        "-" +
        process.env.ENV
    );
    if (!connectionId) return;

    var params = {
      TableName:
        "User-" +
        process.env.API_MYVILLAGEPROJECTADMI_GRAPHQLAPIIDOUTPUT +
        "-" +
        process.env.ENV,
      Key: {
        id: { S: userId },
      },
      ExpressionAttributeNames: {
        "#SC": "socketConnection",
      },
      ExpressionAttributeValues: {
        ":sc": {
          S: connectionId,
        },
      },
      ReturnValues: "ALL_NEW",
      UpdateExpression: "SET #SC = :sc",
    };

    console.log("params",params)

    let updatePostData = await ddb.updateItem(params).promise();
    const updatedUser = AWS.DynamoDB.Converter.unmarshall(
      updatePostData["Attributes"]
    );

    return updatedUser;
    // return true;
  } catch (error) {
    const err = createErrorResponse(error);
    console.log("error", err);
  }
};

const sendMessage = async (connectionId, body, ENDPOINT) => {
  try {
    if (!connectionId) return;

    if (typeof body != Object) {
      body = JSON.parse(body);
    }
    const { senderId, recipientId, channelId, content, messageBy } = body;

    const senderParams = {
      TableName: `User-${process.env.API_MYVILLAGEPROJECTADMI_GRAPHQLAPIIDOUTPUT}-${process.env.ENV}`,
      Key: { id: { S: senderId } },
    };
    const getUserResponse = await ddb.getItem(senderParams).promise();
    console.log("getUserResponse", getUserResponse);
    const senderUser = getUserResponse["Item"]
      ? AWS.DynamoDB.Converter.unmarshall(getUserResponse["Item"])
      : null;
    console.log("senderUser", senderUser);
    const recipientParams = {
      TableName: `User-${process.env.API_MYVILLAGEPROJECTADMI_GRAPHQLAPIIDOUTPUT}-${process.env.ENV}`,
      Key: { id: { S: recipientId ? recipientId : senderId } },
    };
    const recipientParamsGetUserResponse = await ddb
      .getItem(recipientParams)
      .promise();
    console.log(
      "recipientParamsGetUserResponse",
      recipientParamsGetUserResponse
    );
    const recipientUser = recipientParamsGetUserResponse["Item"]
      ? AWS.DynamoDB.Converter.unmarshall(
          recipientParamsGetUserResponse["Item"]
        )
      : null;

    console.log("senderUser", senderUser, "recipientUser", recipientUser);
    if (senderUser && recipientUser) {
      let createMessageId = uuid.v4();
      let date = new Date();

      if (messageBy == "USER") {
        let createMessageParams = {
          Item: {
            id: {
              S: createMessageId,
            },
            __typename: { S: "Message" },
            senderId: {
              S: senderId,
            },
            // recipientId: {
            //   S: recipientId,
            // },
            channelId: {
              S: channelId,
            },
            content: {
              S: content,
            },
            messageBy: {
              S: messageBy,
            },
            isRead: {
              BOOL: false,
            },
            isDeleted: {
              S: "false",
            },
            createdAt: { S: date.toISOString() },
            updatedAt: { S: date.toISOString() },
            _version: { N: "1" },
            _lastChangedAt: { N: date.getTime().toString() },
          },
          TableName:
            "Message-" +
            process.env.API_MYVILLAGEPROJECTADMI_GRAPHQLAPIIDOUTPUT +
            "-" +
            process.env.ENV,
        };
        console.log("createMessageParams", createMessageParams);

        await ddb.putItem(createMessageParams).promise();

        const params = {
          TableName: `Message-${process.env.API_MYVILLAGEPROJECTADMI_GRAPHQLAPIIDOUTPUT}-${process.env.ENV}`,
          Key: { id: { S: createMessageId } },
        };
        const createMessage = await ddb.getItem(params).promise();
        const createMessageRes = createMessage["Item"]
          ? AWS.DynamoDB.Converter.unmarshall(createMessage["Item"])
          : null;

        console.log("createMessageRes", createMessageRes);
        let sendMessageObj = {};
        sendMessageObj["sendMessage"] = {
          senderId: createMessageRes.senderId,
          content: createMessageRes.content,
          channelId: createMessageRes.channelId,
          messageBy: createMessageRes.messageBy,
          givenName: senderUser.name,
          imageUrl: senderUser.imageUrl ? senderUser.imageUrl : "",
          createdAt: createMessageRes.createdAt,
        };

        const clientApi = new ApiGatewayManagementApiClient({
          endpoint: ENDPOINT,
        });
        const requestParams = {
          ConnectionId: connectionId,
          Data: JSON.stringify(sendMessageObj),
        };
        const command = new PostToConnectionCommand(requestParams);
        const resApi = await clientApi.send(command);

        const scanParams = {
          ExpressionAttributeValues: {
            ":d": { S: "false" },
            ":r": { S: "SUPER_ADMIN" },
          },
          FilterExpression: "#D = :d and #R = :r",
          ExpressionAttributeNames: {
            "#D": "isDeleted",
            "#R": "role",
          },
          ProjectionExpression: "id, socketConnection, givenName",
          TableName: `User-${process.env.API_MYVILLAGEPROJECTADMI_GRAPHQLAPIIDOUTPUT}-${process.env.ENV}`,
        };
        const scanResponse = await ddb.scan(scanParams).promise();
        let records = scanResponse.Items.map((item) =>
          AWS.DynamoDB.Converter.unmarshall(item)
        );
        await records.map(async (item) => {
          if (item.socketConnection) {
            let sendMessageObj = {};
            sendMessageObj["sendMessage"] = {
              senderId: createMessageRes.senderId,
              content: createMessageRes.content,
              channelId: createMessageRes.channelId,
              messageBy: createMessageRes.messageBy,
              givenName: senderUser.name,
              imageUrl: senderUser.imageUrl ? senderUser.imageUrl : "",
              createdAt: createMessageRes.createdAt,
            };

            if (senderUser.role === "SUPER_ADMIN") {
              if (senderUser.id !== item.id) {
                const requestParams = {
                  ConnectionId: item.socketConnection,
                  Data: JSON.stringify(sendMessageObj),
                };
                const clientApi = new ApiGatewayManagementApiClient({
                  endpoint: ENDPOINT,
                });
                const command = new PostToConnectionCommand(requestParams);
                const resApi = await clientApi.send(command);

                const refreshChannels = await getAllChannels(
                  item.socketConnection,
                  ENDPOINT
                );
                console.log("refreshChannels", refreshChannels);
              }
            } else {
              const requestParams = {
                ConnectionId: item.socketConnection,
                Data: JSON.stringify(sendMessageObj),
              };
              const clientApi = new ApiGatewayManagementApiClient({
                endpoint: ENDPOINT,
              });
              const command = new PostToConnectionCommand(requestParams);
              const resApi = await clientApi.send(command);

              const refreshChannels = await getAllChannels(
                item.socketConnection,
                ENDPOINT
              );
              console.log("refreshChannels", refreshChannels);
            }
          }
        });

        return true;
      } else if (
        senderUser.role == "SUPER_ADMIN" &&
        messageBy == "SUPERADMIN"
      ) {
        let createMessageParams = {
          Item: {
            id: {
              S: createMessageId,
            },
            __typename: { S: "Message" },
            senderId: {
              S: senderId,
            },
            recipientId: {
              S: recipientId,
            },
            channelId: {
              S: channelId,
            },
            content: {
              S: content,
            },
            messageBy: {
              S: messageBy,
            },
            isRead: {
              BOOL: true,
            },
            isDeleted: {
              S: "false",
            },
            seenBy: {
              S: senderId,
            },
            createdAt: { S: date.toISOString() },
            updatedAt: { S: date.toISOString() },
            _version: { N: "1" },
            _lastChangedAt: { N: date.getTime().toString() },
          },
          TableName:
            "Message-" +
            process.env.API_MYVILLAGEPROJECTADMI_GRAPHQLAPIIDOUTPUT +
            "-" +
            process.env.ENV,
        };
        console.log("createMessageParams", createMessageParams);

        await ddb.putItem(createMessageParams).promise();

        const params = {
          TableName: `Message-${process.env.API_MYVILLAGEPROJECTADMI_GRAPHQLAPIIDOUTPUT}-${process.env.ENV}`,
          Key: { id: { S: createMessageId } },
        };
        const createMessage = await ddb.getItem(params).promise();
        console.log("createMessage", createMessage);
        const createMessageRes = createMessage["Item"]
          ? AWS.DynamoDB.Converter.unmarshall(createMessage["Item"])
          : null;
        const scanParams = {
          ExpressionAttributeValues: {
            ":d": { S: "false" },
            ":r": { S: "SUPER_ADMIN" },
          },
          FilterExpression: "#D = :d and #R = :r",
          ExpressionAttributeNames: {
            "#D": "isDeleted",
            "#R": "role",
          },
          ProjectionExpression: "id, socketConnection, givenName",
          TableName: `User-${process.env.API_MYVILLAGEPROJECTADMI_GRAPHQLAPIIDOUTPUT}-${process.env.ENV}`,
        };
        const scanResponse = await ddb.scan(scanParams).promise();
        let records = scanResponse.Items.map((item) =>
          AWS.DynamoDB.Converter.unmarshall(item)
        );
        await records.map(async (item) => {
          if (item.socketConnection) {
            let sendMessageObj = {};
            sendMessageObj["sendMessage"] = {
              senderId: createMessageRes.senderId,
              content: createMessageRes.content,
              channelId: createMessageRes.channelId,
              messageBy: createMessageRes.messageBy,
              givenName: "MVP Support",
              createdAt: createMessageRes.createdAt,
            };
            const requestParams = {
              ConnectionId: item.socketConnection,
              Data: JSON.stringify(sendMessageObj),
            };
            const clientApi = new ApiGatewayManagementApiClient({
              endpoint: ENDPOINT,
            });
            const command = new PostToConnectionCommand(requestParams);
            const resApi = await clientApi.send(command);
            const refreshChannels = await getAllChannels(
              item.socketConnection,
              ENDPOINT
            );
          }
        });
        let sendMessageObj = {};
        sendMessageObj["sendMessage"] = {
          senderId: createMessageRes.senderId,
          content: createMessageRes.content,
          channelId: createMessageRes.channelId,
          messageBy: createMessageRes.messageBy,
          givenName: "MVP Support",
          createdAt: createMessageRes.createdAt,
        };
        const clientApi = new ApiGatewayManagementApiClient({
          endpoint: ENDPOINT,
        });
        console.log("clientApi", clientApi);
        if (senderId != recipientId && recipientUser.role != "SUPER_ADMIN") {
          const requestParams = {
            ConnectionId: recipientUser.socketConnection,
            Data: JSON.stringify(sendMessageObj),
          };
          const command = new PostToConnectionCommand(requestParams);
          console.log("command", command);
          const resApi = await clientApi.send(command);
          console.log("resApi", resApi);
        }
      }
      return true;
    }
  } catch (e) {
    console.log("in error")
    console.log("Error in on_message", e.message);
  }
};

const onDisconnect = async (connectionId) => {
  try {
    const scanParams = {
      ExpressionAttributeValues: {
        ":d": { S: "false" },
        ":sc": { S: connectionId },
      },
      FilterExpression: "#D = :d and #SC = :sc",
      ExpressionAttributeNames: {
        "#D": "isDeleted",
        "#SC": "socketConnection",
      },
      ProjectionExpression: "id,#SC",
      TableName: `User-${process.env.API_MYVILLAGEPROJECTADMI_GRAPHQLAPIIDOUTPUT}-${process.env.ENV}`,
    };
    const scanResponse = await ddb.scan(scanParams).promise();
    let records = scanResponse.Items.map((item) =>
      AWS.DynamoDB.Converter.unmarshall(item)
    );

    console.log("records", records);
    if (records && records.length > 0) {
      const updateMemberParams = {
        TableName: `User-${process.env.API_MYVILLAGEPROJECTADMI_GRAPHQLAPIIDOUTPUT}-${process.env.ENV}`,
        Key: {
          id: { S: records[0]?.id },
        },
        ExpressionAttributeNames: {
          "#SC": "socketConnection",
        },
        ExpressionAttributeValues: {
          ":sc": { S: "" },
        },
        ReturnValues: "ALL_NEW",
        UpdateExpression: "SET #SC = :sc",
      };
      // console.log("updateMemberParams", updateMemberParams);

      await ddb.updateItem(updateMemberParams).promise();
    }
  } catch (error) {
    const err = createErrorResponse(error);
    console.log("error", err);
  }
};

const getAllChannels = async (connectionId, ENDPOINT) => {
  console.log('connectionId: ', connectionId);
  console.log('ENDPOINT: ', ENDPOINT);
  try {
    const channelTable = `Channel-${process.env.API_MYVILLAGEPROJECTADMI_GRAPHQLAPIIDOUTPUT}-${process.env.ENV}`;
    const messageTable = `Message-${process.env.API_MYVILLAGEPROJECTADMI_GRAPHQLAPIIDOUTPUT}-${process.env.ENV}`;
    const userTable = `User-${process.env.API_MYVILLAGEPROJECTADMI_GRAPHQLAPIIDOUTPUT}-${process.env.ENV}`;
    
    console.log('channelTable: ', channelTable);
    console.log('messageTable: ', messageTable);
    console.log('userTable: ', userTable);

    // Scan all channels
    const channelParams = {
      TableName: channelTable,
      ProjectionExpression: "id",
    };
    console.log('channelParams: ', channelParams);
    const channelResult = await ddb.scan(channelParams).promise();
    const channels = channelResult.Items.map((item) => AWS.DynamoDB.Converter.unmarshall(item));

    if (!channels || channels.length === 0) {
      console.log("No channels found");
      return;
    }

    // Fetch messages and user details for each channel
      const finalData = await Promise.all(
      channels.map(async (channel) => {
        try {

          const messageParams = {
            ExpressionAttributeValues: { ":c": { S: channel.id }, ":d": { S: "false" } },
            FilterExpression: "#C = :c and #D = :d",
            ExpressionAttributeNames: { "#C": "channelId", "#MESSAGEID": "id", "#D": "isDeleted" },
            ProjectionExpression: "#MESSAGEID,content,channelId,createdAt,messageBy,recipientId,senderId,isRead,seenBy",
            TableName: messageTable,
          };
          const messageResult = await ddb.scan(messageParams).promise();
          const messages = messageResult.Items.map((item) => AWS.DynamoDB.Converter.unmarshall(item));

          if (!messages || messages.length === 0) {
            console.log("No messages found for channel:", channel.id);
            return;
          }

          // Count unread messages and sort messages by creation date
          const unreadCount = messages.filter((msg) => !msg.isRead).length;
          messages.sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt));

          const latestMessage = messages[0];
          latestMessage.count = unreadCount;

          // Fetch user details
          const userId = latestMessage.messageBy === "USER" ? latestMessage.senderId : latestMessage.recipientId;
          const userParams = {
            TableName: userTable,
            Key: { id: { S: userId } },
                ProjectionExpression: "givenName,familyName,imageUrl",
              };
          const userResult = await ddb.getItem(userParams).promise();
          const user = userResult.Item ? AWS.DynamoDB.Converter.unmarshall(userResult.Item) : {};

          return { ...channel, ...latestMessage, ...user };
        } catch (error) {
          console.error("Error processing channel:", channel.id, error);
          return undefined;
          }
        })
      );

    // Filter out undefined values and sort by creation date
      const validData = finalData.filter((item) => item !== undefined);
      validData.sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt));

    const responsePayload = {
      channelList: { items: validData },
    };

    // Send response via WebSocket
    const clientApi = new ApiGatewayManagementApiClient({ endpoint: ENDPOINT });
    const command = new PostToConnectionCommand({
        ConnectionId: connectionId,
      Data: JSON.stringify(responsePayload),
    });
    await clientApi.send(command);

    console.log("Response sent successfully");
    return responsePayload;
  } catch (error) {
    console.error("Error in getAllChannels:", error);
    const err = createErrorResponse(error);
    console.log("Error response:", err);
  }
};



const getAllMessages = async (connectionId, body, ENDPOINT) => {
  try {
    if (!connectionId) return;

    if (typeof body != Object) {
      body = JSON.parse(body);
    }
    const messageObj = {};
    console.log("body===>", body);
    const { channelId, type, adminId } = body;
    console.log("channelId==>", channelId);

    const params = {
      TableName: `User-${process.env.API_MYVILLAGEPROJECTADMI_GRAPHQLAPIIDOUTPUT}-${process.env.ENV}`,
      Key: { id: { S: channelId } },
      ProjectionExpression: "givenName,familyName,imageUrl",
    };
    const getUserResponse = await ddb.getItem(params).promise();
    let userItem = getUserResponse["Item"]
      ? AWS.DynamoDB.Converter.unmarshall(getUserResponse["Item"])
      : null;
    const scanParams = {
      ExpressionAttributeValues: {
        ":c": { S: channelId },
        ":d": { S: "false" },
      },
      FilterExpression: "#C = :c and #D = :d",
      ExpressionAttributeNames: { "#C": "channelId", "#D": "isDeleted" },
      ProjectionExpression:
        "id,content,channelId,createdAt,messageBy,recipientId,senderId,isRead,seenBy",
      TableName: `Message-${process.env.API_MYVILLAGEPROJECTADMI_GRAPHQLAPIIDOUTPUT}-${process.env.ENV}`,
    };
    const scanResponse = await ddb.scan(scanParams).promise();
    let newResponse = scanResponse.Items.map((records) =>
      AWS.DynamoDB.Converter.unmarshall(records)
    );

    let finalData = await Promise.all(
      newResponse.map(async (item) => {
        if (type == "SUPERADMIN") {
          if (item.isRead == false) {
            console.log("insideif finalData");
            let updateMessageParams = {
              TableName: `Message-${process.env.API_MYVILLAGEPROJECTADMI_GRAPHQLAPIIDOUTPUT}-${process.env.ENV}`,
              Key: { id: { S: item.id } },
              ExpressionAttributeNames: { "#IR": "isRead", "#SB": "seenBy" },
              ExpressionAttributeValues: {
                ":ir": { BOOL: true },
                ":sb": { S: adminId },
              },
              ReturnValues: "ALL_NEW",
              UpdateExpression: "SET #IR = :ir, #SB= :sb",
            };
            await ddb.updateItem(updateMessageParams).promise();
          }
          console.log("item.isRead", item.isRead);
          console.log(
            "item.isRead == false ? adminId : item.seenBy",
            item.isRead == false ? adminId : item.seenBy
          );
          const params = {
            TableName: `User-${process.env.API_MYVILLAGEPROJECTADMI_GRAPHQLAPIIDOUTPUT}-${process.env.ENV}`,
            Key: { id: { S: item.isRead == false ? adminId : item.seenBy } },
            ProjectionExpression: "givenName,familyName,imageUrl",
          };
          const getUserResponse = await ddb.getItem(params).promise();
          let adminData = getUserResponse["Item"]
            ? AWS.DynamoDB.Converter.unmarshall(getUserResponse["Item"])
            : null;
          console.log("adminData", adminData);
          return {
            ...item,
            givenName:
              item.messageBy == "SUPERADMIN"
                ? adminData.givenName
                : userItem.givenName,
            familyName:
              item.messageBy == "SUPERADMIN"
                ? adminData.familyName
                : userItem.familyName,
            imageUrl:
              item.messageBy == "SUPERADMIN"
                ? adminData.imageUrl
                : userItem.imageUrl,
            seenByAdminName: adminData.givenName ? adminData.givenName : "",
          };
        } else {
          return {
            ...item,
            givenName: userItem.givenName,
            familyName: userItem.familyName,
            imageUrl: userItem.imageUrl ? userItem.imageUrl : "",
          };
        }
      })
    );
    console.log("finalData", finalData);
    finalData.sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt));
    messageObj["messageList"] = { items: finalData };
    const clientApi = new ApiGatewayManagementApiClient({
      endpoint: ENDPOINT,
    });
    const requestParams = {
      ConnectionId: connectionId,
      Data: JSON.stringify(messageObj),
    };
    const command = new PostToConnectionCommand(requestParams);
    const resApi = await clientApi.send(command);
  } catch (error) {
    const err = createErrorResponse(error);
    console.log("error==>", err);
  }
};

const clearMessage = async (connectionId, body, ENDPOINT) => {
  try {
    if (!connectionId) return;

    if (typeof body != Object) {
      body = JSON.parse(body);
    }
    const scanParams = {
      ExpressionAttributeValues: {
        ":d": { S: "false" },
        ":c": { S: body.channelId },
      },
      FilterExpression: "#D = :d and #C = :c",
      ExpressionAttributeNames: {
        "#D": "isDeleted",
        "#C": "channelId",
      },
      ProjectionExpression: "id",
      TableName: `Message-${process.env.API_MYVILLAGEPROJECTADMI_GRAPHQLAPIIDOUTPUT}-${process.env.ENV}`,
    };
    const scanResponse = await ddb.scan(scanParams).promise();
    let records = scanResponse.Items.map((item) =>
      AWS.DynamoDB.Converter.unmarshall(item)
    );
    console.log("records", records);
    if (records && records.length > 0) {
      const data = await Promise.all(
        records.map(async (item) => {
          var updateParams = {
            TableName: `Message-${process.env.API_MYVILLAGEPROJECTADMI_GRAPHQLAPIIDOUTPUT}-${process.env.ENV}`,

            Key: {
              id: { S: item.id },
            },
            ExpressionAttributeNames: {
              "#D": "isDeleted",
            },
            ExpressionAttributeValues: {
              ":d": {
                S: "true",
              },
            },
            ReturnValues: "ALL_NEW",
            UpdateExpression: "SET #D = :d",
          };
          await ddb.updateItem(updateParams).promise();
        })
      );
      let res = {
        statusCode: 200,
        message: "Clear message successfully",
      };
      const clientApi = new ApiGatewayManagementApiClient({
        endpoint: ENDPOINT,
      });
      const requestParams = {
        ConnectionId: connectionId,
        Data: JSON.stringify({ clearMessage: res }),
      };
      const command = new PostToConnectionCommand(requestParams);
      const resApi = await clientApi.send(command);
    } else {
      let res = {
        statusCode: 200,
        message: "No message to clear",
      };
      const clientApi = new ApiGatewayManagementApiClient({
        endpoint: ENDPOINT,
      });
      const requestParams = {
        ConnectionId: connectionId,
        Data: JSON.stringify({ clearMessage: res }),
      };
      const command = new PostToConnectionCommand(requestParams);
      const resApi = await clientApi.send(command);
    }
  } catch (error) {
    const err = createErrorResponse(error);
    console.log("error", err);
  }
};

module.exports = { handler };
