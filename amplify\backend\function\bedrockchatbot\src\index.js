
const AWS = require("aws-sdk");
const { createLogger, createBedrockLogger, logError, logSuccess, logPerformance } = require('./utils/logger');

// Configure AWS SDK
AWS.config.update({
  region: process.env.REGION,
});

/**
 * @type {import('@types/aws-lambda').APIGatewayProxyHandler}
 */
exports.handler = async (event, context) => {
  const logger = createLogger(context, {
    functionName: 'bedrockchatbot',
    fieldName: event.fieldName
  });

  const startTime = Date.now();

  logger.info({
    fieldName: event.fieldName,
    hasArguments: !!event.arguments,
    environment: {
      env: process.env.ENV,
      region: process.env.REGION,
      awsRegion: process.env.AWS_REGION,
      hasAccessKey: !!process.env.AWS_ACCESS_KEY_ID,
      hasSecretKey: !!process.env.AWS_SECRET_ACCESS_KEY
    }
  }, "Bedrock Chatbot Handler Started");

  let fieldName = event.fieldName;
  let args = event.arguments;

  logger.debug({
    fieldName,
    argumentsProvided: !!args
  }, `Processing field: ${fieldName}`);

  try {
    if (fieldName === "testBedrockChat") {
      const bedrockLogger = createBedrockLogger(context, 'testBedrockChat', 'mock-bedrock-model', {
        userId: args.input?.userId,
        sessionId: args.input?.sessionId
      });

      bedrockLogger.info({
        operation: 'testBedrockChat',
        inputProvided: !!args.input
      }, 'Starting Bedrock chat test');

      try {
        const { message, userId, sessionId } = args.input;

        bedrockLogger.debug({
          messageLength: message?.length,
          userId,
          sessionId,
          messageType: typeof message
        }, 'Extracted input parameters');

        if (!message || message.trim().length === 0) {
          bedrockLogger.warn({
            message,
            reason: 'invalid_message'
          }, 'Invalid message provided');

          return {
            statusCode: 400,
            message: "Invalid message provided",
            data: null,
          };
        }

        bedrockLogger.info({
          messageLength: message.length,
          operation: 'process_chat'
        }, 'Processing chat request');

        // For now, we'll return a mock response since Bedrock integration needs proper setup
        const mockResponse = await processChatMessage(message, userId, sessionId, bedrockLogger);

        logSuccess(bedrockLogger, 'testBedrockChat', mockResponse, {
          responseLength: mockResponse.response?.length,
          tokensUsed: mockResponse.usage?.totalTokens,
          duration: Date.now() - startTime
        });

        const response = {
          statusCode: 200,
          message: "Chat processed successfully",
          data: mockResponse,
        };

        return response;
      } catch (error) {
        logError(bedrockLogger, error, 'testBedrockChat', {
          userId: args.input?.userId,
          sessionId: args.input?.sessionId,
          duration: Date.now() - startTime
        });

        return {
          statusCode: 500,
          message: error?.message || "Failed to process chat",
          data: null,
        };
      }
    } else {
      logger.warn({
        fieldName,
        reason: 'unsupported_operation'
      }, `Unsupported field name: ${fieldName}`);

      return {
        statusCode: 400,
        message: "Unsupported operation: " + fieldName,
        data: null,
      };
    }
  } catch (error) {
    logError(logger, error, 'bedrockchatbot_handler', {
      fieldName: event.fieldName,
      hasArguments: !!event.arguments,
      duration: Date.now() - startTime
    });

    return {
      statusCode: 500,
      message: error.message || "An error occurred",
      data: null,
    };
  }
};

// Mock function to simulate Bedrock chat processing
async function processChatMessage(message, userId, sessionId, logger = null) {
  if (logger) {
    logger.info({
      messageLength: message?.length,
      userId,
      sessionId,
      operation: 'process_chat_message'
    }, 'Starting chat processing');
  }

  // Simulate processing time
  await new Promise(resolve => setTimeout(resolve, 100));

  const timestamp = new Date().toISOString();
  const generatedSessionId = sessionId || `session-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`;

  // Generate a mock response based on the input message
  let mockResponse = `Hello! I received your message: "${message}". This is a test response from the Bedrock chatbot integration.`;

  if (message.toLowerCase().includes('hello')) {
    mockResponse = "Hello! How can I assist you today?";
  } else if (message.toLowerCase().includes('test')) {
    mockResponse = "This is a test response. The Bedrock chatbot integration is working correctly!";
  } else if (message.toLowerCase().includes('help')) {
    mockResponse = "I'm here to help! This is a mock response from the Bedrock chatbot. The integration is ready for real Bedrock model implementation.";
  }

  const responseData = {
    response: mockResponse,
    sessionId: generatedSessionId,
    timestamp: timestamp,
    model: "mock-bedrock-model",
    usage: {
      inputTokens: Math.ceil(message.length / 4), // Rough token estimation
      outputTokens: Math.ceil(mockResponse.length / 4),
      totalTokens: Math.ceil((message.length + mockResponse.length) / 4)
    }
  };

  if (logger) {
    logger.info({
      responseLength: mockResponse.length,
      sessionId: generatedSessionId,
      tokensUsed: responseData.usage.totalTokens,
      operation: 'process_chat_message'
    }, 'Chat processing completed successfully');
  }

  return responseData;
}
