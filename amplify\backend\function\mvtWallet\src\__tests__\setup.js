/**
 * Jest Test Setup
 * Global mocks and test utilities for MVT Wallet Lambda function
 */

// Mock AWS SDK
const mockDynamoDB = {
  getItem: jest.fn(),
  putItem: jest.fn(),
  updateItem: jest.fn(),
  scan: jest.fn(),
  query: jest.fn(),
  describeTable: jest.fn(),
  batchGetItem: jest.fn(),
  batchWriteItem: jest.fn()
};

const mockDocumentClient = {
  get: jest.fn(),
  put: jest.fn(),
  update: jest.fn(),
  scan: jest.fn(),
  query: jest.fn(),
  batchGet: jest.fn(),
  batchWrite: jest.fn()
};

// Mock AWS module
jest.mock('aws-sdk', () => {
  const mockAWS = {
    DynamoDB: jest.fn(() => mockDynamoDB),
    config: {
      update: jest.fn()
    }
  };

  // Add DocumentClient as a property of DynamoDB
  mockAWS.DynamoDB.DocumentClient = jest.fn(() => mockDocumentClient);

  // Add Converter as a property of DynamoDB
  mockAWS.DynamoDB.Converter = {
    marshall: jest.fn((item) => item),
    unmarshall: jest.fn((item) => item)
  };

  return mockAWS;
});

// Mock ethers for blockchain operations
jest.mock('ethers', () => ({
  ethers: {
    JsonRpcProvider: jest.fn(() => ({
      getBalance: jest.fn().mockResolvedValue('1000000000000000000'),
      getNetwork: jest.fn().mockResolvedValue({ chainId: 1337 })
    })),
    Wallet: jest.fn(() => ({
      address: '******************************************',
      connect: jest.fn()
    })),
    Contract: jest.fn(() => ({
      balanceOf: jest.fn().mockResolvedValue('1000000'),
      transfer: jest.fn().mockResolvedValue({ hash: '0xabcdef' }),
      deposit: jest.fn().mockResolvedValue({ hash: '0xabcdef' }),
      withdraw: jest.fn().mockResolvedValue({ hash: '0xabcdef' })
    })),
    formatUnits: jest.fn((value) => (parseFloat(value) / 1000000).toString()),
    parseUnits: jest.fn((value) => (parseFloat(value) * 1000000).toString())
  }
}));

// Global test utilities
global.testUtils = {
  // Mock GraphQL event
  createMockEvent: (cognitoIdentityId = 'test-cognito-id', isAdmin = false) => ({
    requestContext: {
      identity: {
        cognitoIdentityId
      }
    },
    arguments: {},
    info: {
      fieldName: 'testField'
    },
    source: {},
    stateValues: {},
    prev: null
  }),

  // Mock GraphQL arguments
  createMockArgs: (input = {}) => ({
    input
  }),

  // Mock user data
  createMockUser: (userId = 'test-user-123', isAdmin = false) => ({
    id: userId,
    cognitoId: 'test-cognito-id',
    email: '<EMAIL>',
    firstName: 'Test',
    lastName: 'User',
    walletAddress: isAdmin ? null : '******************************************',
    role: isAdmin ? 'SUPER_ADMIN' : 'MEMBER',
    isDeleted: 'false',
    createdAt: '2024-01-01T00:00:00.000Z',
    updatedAt: '2024-01-01T00:00:00.000Z'
  }),

  // Mock wallet balance
  createMockWalletBalance: (mvtBalance = 1000, lockedMVT = 0) => ({
    mvtBalance,
    lockedMVT,
    availableMVT: mvtBalance - lockedMVT,
    lastUpdated: new Date().toISOString()
  }),

  // Mock transaction
  createMockTransaction: (id = 'test-tx-123', type = 'ADMIN_MINT', amount = 100) => ({
    id,
    transactionType: type,
    amount,
    fromWalletId: 'central-mvt-wallet',
    toWalletId: 'user-wallet-test-user-123',
    fromUserId: null,
    toUserId: 'test-user-123',
    status: 'COMPLETED',
    transactionHash: `internal-${id}`,
    internalTxId: id,
    description: `Test ${type} transaction`,
    adminUserId: type.includes('ADMIN') ? 'admin-user-123' : null,
    metadata: JSON.stringify({ test: true }),
    isDeleted: 'false',
    createdAt: '2024-01-01T00:00:00.000Z',
    updatedAt: '2024-01-01T00:00:00.000Z'
  }),

  // Mock swap request
  createMockSwapRequest: (id = 'test-swap-123', status = 'PENDING') => ({
    id,
    userId: 'test-user-123',
    mvtAmount: 100,
    usdcAmount: 50.0,
    exchangeRate: 0.5,
    status,
    userWalletAddress: '******************************************',
    description: 'Test swap request',
    requestedAt: '2024-01-01T00:00:00.000Z',
    processedAt: status !== 'PENDING' ? '2024-01-01T01:00:00.000Z' : null,
    adminUserId: status !== 'PENDING' ? 'admin-user-123' : null,
    transactionHash: status === 'APPROVED' ? '0xabcdef' : null,
    isDeleted: 'false',
    createdAt: '2024-01-01T00:00:00.000Z',
    updatedAt: '2024-01-01T00:00:00.000Z'
  }),

  // Mock DynamoDB responses
  mockDynamoDBSuccess: (data = {}) => ({
    promise: jest.fn().mockResolvedValue(data)
  }),

  mockDynamoDBError: (error = new Error('DynamoDB error')) => ({
    promise: jest.fn().mockRejectedValue(error)
  }),

  // Mock blockchain responses
  mockBlockchainSuccess: (hash = '0xabcdef') => Promise.resolve({
    hash,
    blockNumber: 12345,
    gasUsed: '21000',
    status: 1
  }),

  mockBlockchainError: (error = new Error('Blockchain error')) => Promise.reject(error)
};

// Global test constants
global.testConstants = {
  CENTRAL_WALLET_ID: 'central-mvt-wallet',
  TEST_USER_ID: 'test-user-123',
  TEST_ADMIN_ID: 'admin-user-123',
  TEST_COGNITO_ID: 'test-cognito-id',
  TEST_WALLET_ADDRESS: '******************************************',
  TEST_TRANSACTION_ID: 'test-tx-123',
  TEST_SWAP_ID: 'test-swap-123'
};

// Reset all mocks before each test
beforeEach(() => {
  jest.clearAllMocks();
  
  // Reset DynamoDB mocks to default success responses
  mockDynamoDB.getItem.mockReturnValue(global.testUtils.mockDynamoDBSuccess());
  mockDynamoDB.putItem.mockReturnValue(global.testUtils.mockDynamoDBSuccess());
  mockDynamoDB.updateItem.mockReturnValue(global.testUtils.mockDynamoDBSuccess());
  mockDynamoDB.scan.mockReturnValue(global.testUtils.mockDynamoDBSuccess({ Items: [] }));
  mockDynamoDB.query.mockReturnValue(global.testUtils.mockDynamoDBSuccess({ Items: [] }));
  mockDynamoDB.describeTable.mockReturnValue(global.testUtils.mockDynamoDBSuccess({
    Table: { TableStatus: 'ACTIVE' }
  }));
});

// Global test environment setup
process.env.NODE_ENV = 'test';
process.env.AWS_REGION = 'us-east-1';
process.env.ENVIRONMENT = 'test';

// Suppress console logs during tests (uncomment to enable logging)
// global.console = {
//   ...console,
//   log: jest.fn(),
//   debug: jest.fn(),
//   info: jest.fn(),
//   warn: jest.fn(),
//   error: jest.fn()
// };

module.exports = {
  mockDynamoDB,
  mockDocumentClient
};
