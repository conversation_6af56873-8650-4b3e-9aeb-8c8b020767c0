const transactionService = require('./transaction.service');
const authService = require('../../shared/services/authService');
const validationUtils = require('../../shared/utils/validationUtils');
const { AuthorizationTypes, executeHandler, handleTransactionListAuthorization } = require('../../shared/utils/handlerUtils');
const standardizedErrorUtils = require('../../shared/utils/standardizedErrorUtils');
const responseUtils = require('../../shared/utils/responseUtils');

/**
 * Handle adminMintMVT request
 * @param {object} event - GraphQL event
 * @param {object} args - GraphQL arguments
 * @returns {Promise<object>} - Response object
 */
async function handleAdminMintMVT(event, args) {
  return executeHandler(event, args, {
    authorization: { type: AuthorizationTypes.ADMIN_ONLY, requireUserId: true },
    validation: validationUtils.validateMintInput,
    operationName: 'adminMintMVT',
    successMessage: `Successfully minted ${args.input.amount} MVT tokens`
  }, async (authContext) => {
    const { amount, description } = args.input;
    const transactionData = await transactionService.mintMVTTokens(
      amount,
      description,
      authContext.adminUserId
    );
    return { data: transactionData };
  });
}

/**
 * Handle adminTransferMVT request
 * @param {object} event - GraphQL event
 * @param {object} args - GraphQL arguments
 * @returns {Promise<object>} - Response object
 */
async function handleAdminTransferMVT(event, args) {
  return executeHandler(event, args, {
    authorization: { type: AuthorizationTypes.ADMIN_ONLY, requireUserId: true },
    validation: validationUtils.validateTransferInput,
    operationName: 'adminTransferMVT',
    successMessage: `Successfully transferred ${args.input.amount} MVT tokens to user`
  }, async (authContext) => {
    const { userId, amount, description } = args.input;
    const transactionData = await transactionService.transferMVTToUser(
      userId,
      amount,
      description,
      authContext.adminUserId
    );
    return { data: transactionData };
  });
}

/**
 * Handle userTransferMVT request
 * @param {object} event - GraphQL event
 * @param {object} args - GraphQL arguments
 * @returns {Promise<object>} - Response object
 */
async function handleUserTransferMVT(event, args) {
  return executeHandler(event, args, {
    authorization: { type: AuthorizationTypes.USER_AUTHENTICATED },
    validation: validationUtils.validateUserTransferInput,
    operationName: 'userTransferMVT',
    successMessage: `Successfully transferred ${args.input.amount} MVT tokens to user`
  }, async (authContext) => {
    const { recipientUserId, amount, description } = args.input;
    
    // Additional authorization check for user transfers
    const isAuthorized = await authService.checkUserAuthorization(event, authContext.currentUserDatabaseId);
    if (!isAuthorized) {
      throw new Error("Unauthorized: You can only transfer your own tokens");
    }

    const transactionData = await transactionService.transferMVTBetweenUsers(
      authContext.currentUserDatabaseId,
      recipientUserId,
      amount,
      description
    );
    return { data: transactionData };
  });
}

/**
 * Handle getMVTWalletTransactionList request
 * @param {object} event - GraphQL event
 * @param {object} args - GraphQL arguments
 * @returns {Promise<object>} - Response object
 */
async function handleGetMVTWalletTransactionList(event, args) {
  try {
    const authResult = await handleTransactionListAuthorization(event, args);
    if (authResult.error) {
      return authResult.error;
    }

    const { address, isAdmin } = authResult.context;
    const { limit = 50 } = args;

    const transactions = await transactionService.getMVTWalletTransactionList(address, isAdmin, limit);
    return responseUtils.createSuccessResponse(
      transactions,
      "Transaction list retrieved successfully"
    );
  } catch (error) {
    console.error("Error in getMVTWalletTransactionList:", error);
    return standardizedErrorUtils.createAutoDetectedError(
      'getMVTWalletTransactionList',
      error,
      'transaction list retrieval'
    );
  }
}

/**
 * Handle getMVTWalletTransactionById request
 * @param {object} event - GraphQL event
 * @param {object} args - GraphQL arguments
 * @returns {Promise<object>} - Response object
 */
async function handleGetMVTWalletTransactionById(event, args) {
  try {
    const { transactionId } = args;

    // Get user ID for authorization
    const currentUserId = await authService.getUserIdFromEvent(event);
    if (!currentUserId) {
      return standardizedErrorUtils.createAuthorizationError(
        'getMVTWalletTransactionById',
        'Authentication required'
      );
    }

    const transaction = await transactionService.getMVTWalletTransactionById(transactionId, currentUserId, event);
    if (!transaction) {
      return standardizedErrorUtils.createValidationError(
        'getMVTWalletTransactionById',
        'Transaction not found',
        'transactionId'
      );
    }

    return responseUtils.createSuccessResponse(
      transaction,
      "Transaction retrieved successfully"
    );
  } catch (error) {
    console.error("Error in getMVTWalletTransactionById:", error);
    return standardizedErrorUtils.createAutoDetectedError(
      'getMVTWalletTransactionById',
      error,
      'transaction retrieval'
    );
  }
}

module.exports = {
  handleAdminMintMVT,
  handleAdminTransferMVT,
  handleUserTransferMVT,
  handleGetMVTWalletTransactionList,
  handleGetMVTWalletTransactionById
};
