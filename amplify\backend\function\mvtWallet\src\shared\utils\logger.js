/**
 * Shared Logging Utility for AWS Lambda Functions
 * 
 * This module provides a structured logging solution using Pino,
 * optimized for AWS Lambda environments with CloudWatch integration.
 * 
 * Features:
 * - Structured JSON logging
 * - AWS Lambda context integration (requestId, functionName)
 * - Environment-based log levels
 * - Performance optimized for serverless
 * - CloudWatch compatible formatting
 */

const pino = require('pino');

/**
 * Default log level configuration based on environment
 */
const getLogLevel = () => {
  // Use AWS Lambda log level if set, otherwise default based on environment
  if (process.env.AWS_LAMBDA_LOG_LEVEL) {
    return process.env.AWS_LAMBDA_LOG_LEVEL.toLowerCase();
  }
  
  const env = process.env.ENV || process.env.NODE_ENV || 'development';
  
  switch (env.toLowerCase()) {
    case 'prod':
    case 'production':
      return 'warn';
    case 'dev':
    case 'development':
      return 'debug';
    case 'amplifydev':
    case 'staging':
      return 'info';
    default:
      return 'info';
  }
};

/**
 * Pino configuration optimized for AWS Lambda
 */
const pinoConfig = {
  level: getLogLevel(),
  
  // Custom formatters for AWS Lambda compatibility
  formatters: {
    // Add Lambda-specific context to all logs
    bindings: (bindings) => {
      return {
        nodeVersion: process.version,
        functionName: process.env.AWS_LAMBDA_FUNCTION_NAME || 'unknown',
        functionVersion: process.env.AWS_LAMBDA_FUNCTION_VERSION || '$LATEST',
        environment: process.env.ENV || 'unknown'
      };
    },
    
    // Ensure log levels are uppercase for CloudWatch compatibility
    level: (label) => {
      return { level: label.toUpperCase() };
    }
  },
  
  // Custom timestamp format for AWS Lambda
  timestamp: () => `,"timestamp":"${new Date().toISOString()}"`,
  
  // Optimize for Lambda - disable pretty printing in production
  prettyPrint: false,
  
  // Ensure logs are flushed immediately (important for Lambda)
  sync: true
};

/**
 * Base logger instance
 */
const baseLogger = pino(pinoConfig);

/**
 * Create a child logger with Lambda context
 * 
 * @param {Object} context - AWS Lambda context object
 * @param {Object} additionalFields - Additional fields to include in all logs
 * @returns {Object} Configured Pino logger instance
 */
function createLogger(context = {}, additionalFields = {}) {
  const contextFields = {
    requestId: context.awsRequestId || 'unknown',
    remainingTimeMs: context.getRemainingTimeInMillis ? context.getRemainingTimeInMillis() : undefined,
    ...additionalFields
  };
  
  // Remove undefined values
  Object.keys(contextFields).forEach(key => {
    if (contextFields[key] === undefined) {
      delete contextFields[key];
    }
  });
  
  return baseLogger.child(contextFields);
}

/**
 * Create a logger with user context
 * 
 * @param {Object} context - AWS Lambda context object
 * @param {string} userId - User ID for correlation
 * @param {Object} additionalFields - Additional fields to include
 * @returns {Object} Configured Pino logger instance with user context
 */
function createUserLogger(context = {}, userId = null, additionalFields = {}) {
  const userFields = {
    userId: userId || 'anonymous',
    ...additionalFields
  };
  
  return createLogger(context, userFields);
}

/**
 * Create a logger for database operations
 * 
 * @param {Object} context - AWS Lambda context object
 * @param {string} operation - Database operation name
 * @param {string} tableName - DynamoDB table name
 * @param {Object} additionalFields - Additional fields
 * @returns {Object} Configured Pino logger instance for database operations
 */
function createDatabaseLogger(context = {}, operation = '', tableName = '', additionalFields = {}) {
  const dbFields = {
    operation,
    tableName,
    component: 'database',
    ...additionalFields
  };
  
  return createLogger(context, dbFields);
}

/**
 * Create a logger for blockchain operations
 * 
 * @param {Object} context - AWS Lambda context object
 * @param {string} operation - Blockchain operation name
 * @param {string} contractAddress - Smart contract address
 * @param {Object} additionalFields - Additional fields
 * @returns {Object} Configured Pino logger instance for blockchain operations
 */
function createBlockchainLogger(context = {}, operation = '', contractAddress = '', additionalFields = {}) {
  const blockchainFields = {
    operation,
    contractAddress,
    component: 'blockchain',
    ...additionalFields
  };
  
  return createLogger(context, blockchainFields);
}

/**
 * Log performance metrics
 * 
 * @param {Object} logger - Pino logger instance
 * @param {string} operation - Operation name
 * @param {number} startTime - Start time in milliseconds
 * @param {Object} additionalMetrics - Additional metrics to log
 */
function logPerformance(logger, operation, startTime, additionalMetrics = {}) {
  const duration = Date.now() - startTime;
  
  logger.info({
    operation,
    duration,
    performanceMetrics: {
      durationMs: duration,
      ...additionalMetrics
    }
  }, `Performance: ${operation} completed in ${duration}ms`);
}

/**
 * Log error with context
 * 
 * @param {Object} logger - Pino logger instance
 * @param {Error} error - Error object
 * @param {string} operation - Operation that failed
 * @param {Object} context - Additional context
 */
function logError(logger, error, operation = 'unknown', context = {}) {
  logger.error({
    error: {
      name: error.name,
      message: error.message,
      stack: error.stack,
      code: error.code
    },
    operation,
    errorContext: context
  }, `Error in ${operation}: ${error.message}`);
}

/**
 * Log successful operation with result summary
 * 
 * @param {Object} logger - Pino logger instance
 * @param {string} operation - Operation name
 * @param {Object} result - Operation result
 * @param {Object} summary - Summary of the result (avoid logging sensitive data)
 */
function logSuccess(logger, operation, result = {}, summary = {}) {
  logger.info({
    operation,
    success: true,
    resultSummary: summary
  }, `Successfully completed ${operation}`);
}

module.exports = {
  createLogger,
  createUserLogger,
  createDatabaseLogger,
  createBlockchainLogger,
  logPerformance,
  logError,
  logSuccess,
  baseLogger
};
