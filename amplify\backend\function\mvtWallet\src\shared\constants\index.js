// Application constants
const CENTRAL_WALLET_ID = "central-mvt-wallet";

// Admin roles
const ADMIN_ROLES = {
  SUPER_ADMIN: "SUPER_ADMIN",
  STAFF_MEMBER: "STAFF_MEMBER"
};

// Transaction types (must match GraphQL MVTWalletTransactionType enum)
const TRANSACTION_TYPES = {
  // Core wallet operations
  WALLET_MINT: "WALLET_MINT",
  WALLET_TRANSFER: "WALLET_TRANSFER",
  ADMIN_MINT: "ADMIN_MINT",
  ADMIN_TRANSFER: "ADMIN_TRANSFER",

  // User transfer operations
  USER_TO_USER_TRANSFER: "USER_TO_USER_TRANSFER",
  CENTRAL_TO_USER_TRANSFER: "CENTRAL_TO_USER_TRANSFER",
  USER_TO_CENTRAL_TRANSFER: "USER_TO_CENTRAL_TRANSFER",

  // System operations
  REWARD_DISTRIBUTION: "REWARD_DISTRIBUTION",
  PENALTY_DEDUCTION: "PENALTY_DEDUCTION",
  SYSTEM_ADJUSTMENT: "SYSTEM_ADJUSTMENT",

  // USDC operations
  USDC_DEPOSIT: "USDC_DEPOSIT",
  USDC_WITHDRAWAL: "USDC_WITHDRAWAL",

  // Swap operations
  SWAP_REQUEST: "SWAP_REQUEST",
  SWAP_APPROVED: "SWAP_APPROVED",
  SWAP_REJECTED: "SWAP_REJECTED"
};

// Transaction status
const TRANSACTION_STATUS = {
  COMPLETED: "COMPLETED",
  PENDING: "PENDING",
  FAILED: "FAILED",
  CANCELLED: "CANCELLED",
  APPROVED: "APPROVED",
  REJECTED: "REJECTED",
  PROCESSING: "PROCESSING"
};

// Swap request status
const SWAP_STATUS = {
  PENDING: "PENDING",
  PROCESSING: "PROCESSING",
  APPROVED: "APPROVED",
  REJECTED: "REJECTED",
  COMPLETED: "COMPLETED",
  FAILED: "FAILED",
  EXPIRED: "EXPIRED"
};

// Token types for transaction tracking
const TOKEN_TYPES = {
  MVT: "MVT",
  USDC: "USDC"
};

// Response status codes
const STATUS_CODES = {
  SUCCESS: 200,
  BAD_REQUEST: 400,
  UNAUTHORIZED: 401,
  FORBIDDEN: 403,
  NOT_FOUND: 404,
  INTERNAL_ERROR: 500
};

module.exports = {
  CENTRAL_WALLET_ID,
  ADMIN_ROLES,
  TRANSACTION_TYPES,
  TRANSACTION_STATUS,
  SWAP_STATUS,
  TOKEN_TYPES,
  STATUS_CODES
};
