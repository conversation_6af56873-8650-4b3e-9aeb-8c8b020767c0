/**
 * Exchange Rate Handlers Test Suite
 * Tests for exchange rate GraphQL handlers including rate calculations and fallback mechanisms
 */

const exchangeRateHandlers = require('../exchangeRate.handlers');
const exchangeRateService = require('../exchangeRate.service');
const responseUtils = require('../../../shared/utils/responseUtils');

// Mock dependencies
jest.mock('../exchangeRate.service');
jest.mock('../../../shared/utils/responseUtils');

describe('Exchange Rate Handlers', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('handleGetMVTWalletExchangeRate', () => {
    const mockEvent = global.testUtils.createMockEvent('user-cognito-id', false);
    const mockArgs = global.testUtils.createMockArgs();

    test('should successfully retrieve exchange rate', async () => {
      // Arrange
      const mockExchangeRateData = {
        currentRate: 0.5,
        rateDisplay: '1 MVT = 0.50 USDC',
        liquidityStatus: {
          usdcReserves: 10000.0,
          mvtSupply: 20000.0,
          liquidityRatio: 0.5,
          status: 'AVAILABLE'
        },
        lastUpdated: '2024-01-01T00:00:00.000Z'
      };
      
      exchangeRateService.getExchangeRateSummary.mockResolvedValue(mockExchangeRateData);
      responseUtils.createSuccessResponse.mockReturnValue({
        statusCode: 200,
        message: 'Exchange rate retrieved successfully',
        data: mockExchangeRateData
      });

      // Act
      const result = await exchangeRateHandlers.handleGetMVTWalletExchangeRate(mockEvent, mockArgs);

      // Assert
      expect(exchangeRateService.getExchangeRateSummary).toHaveBeenCalled();
      expect(responseUtils.createSuccessResponse).toHaveBeenCalledWith(
        mockExchangeRateData,
        'Exchange rate retrieved successfully'
      );
      expect(result.statusCode).toBe(200);
      expect(result.data).toEqual(mockExchangeRateData);
    });

    test('should handle fallback mode gracefully', async () => {
      // Arrange
      const mockFallbackData = {
        currentRate: 0.5,
        rateDisplay: '1 MVT = 0.50 USDC (Fallback Rate)',
        liquidityStatus: {
          usdcReserves: 500.0,
          mvtSupply: 1000.0,
          liquidityRatio: 0.5,
          status: 'FALLBACK_MODE'
        },
        lastUpdated: '2024-01-01T00:00:00.000Z'
      };
      
      exchangeRateService.getExchangeRateSummary.mockResolvedValue(mockFallbackData);
      responseUtils.createSuccessResponse.mockReturnValue({
        statusCode: 200,
        message: 'Exchange rate retrieved successfully (using fallback values due to liquidity constraints)',
        data: mockFallbackData
      });

      // Act
      const result = await exchangeRateHandlers.handleGetMVTWalletExchangeRate(mockEvent, mockArgs);

      // Assert
      expect(result.statusCode).toBe(200);
      expect(result.message).toContain('fallback values due to liquidity constraints');
      expect(result.data.liquidityStatus.status).toBe('FALLBACK_MODE');
    });

    test('should handle unavailable exchange rates', async () => {
      // Arrange
      const mockUnavailableData = {
        currentRate: null,
        rateDisplay: 'Exchange rates temporarily unavailable',
        liquidityStatus: {
          usdcReserves: 0,
          mvtSupply: 0,
          liquidityRatio: 0,
          status: 'UNAVAILABLE'
        },
        lastUpdated: '2024-01-01T00:00:00.000Z'
      };
      
      exchangeRateService.getExchangeRateSummary.mockResolvedValue(mockUnavailableData);
      responseUtils.createInternalErrorResponse.mockReturnValue({
        statusCode: 500,
        message: 'Exchange rates are currently unavailable. Please check system liquidity and try again.'
      });

      // Act
      const result = await exchangeRateHandlers.handleGetMVTWalletExchangeRate(mockEvent, mockArgs);

      // Assert
      expect(responseUtils.createInternalErrorResponse).toHaveBeenCalledWith(
        'Exchange rates are currently unavailable. Please check system liquidity and try again.'
      );
      expect(result.statusCode).toBe(500);
    });

    test('should handle null/undefined exchange rate data', async () => {
      // Arrange
      exchangeRateService.getExchangeRateSummary.mockResolvedValue(null);
      responseUtils.createInternalErrorResponse.mockReturnValue({
        statusCode: 500,
        message: 'Exchange rate service is currently unavailable. Please try again later.'
      });

      // Act
      const result = await exchangeRateHandlers.handleGetMVTWalletExchangeRate(mockEvent, mockArgs);

      // Assert
      expect(responseUtils.createInternalErrorResponse).toHaveBeenCalledWith(
        'Exchange rate service is currently unavailable. Please try again later.'
      );
      expect(result.statusCode).toBe(500);
    });

    test('should handle service errors gracefully', async () => {
      // Arrange
      exchangeRateService.getExchangeRateSummary.mockRejectedValue(new Error('Database connection failed'));
      responseUtils.handleServiceError.mockReturnValue({
        statusCode: 500,
        message: 'Failed to retrieve exchange rate'
      });

      // Act
      const result = await exchangeRateHandlers.handleGetMVTWalletExchangeRate(mockEvent, mockArgs);

      // Assert
      expect(responseUtils.handleServiceError).toHaveBeenCalledWith(
        expect.any(Error),
        'Failed to retrieve exchange rate'
      );
      expect(result.statusCode).toBe(500);
    });

    test('should handle liquidity calculation errors', async () => {
      // Arrange
      exchangeRateService.getExchangeRateSummary.mockRejectedValue(new Error('Liquidity calculation failed'));

      // Act
      const result = await exchangeRateHandlers.handleGetMVTWalletExchangeRate(mockEvent, mockArgs);

      // Assert
      expect(result.statusCode).toBe(500);
    });

    test('should handle blockchain connectivity issues', async () => {
      // Arrange
      exchangeRateService.getExchangeRateSummary.mockRejectedValue(new Error('Blockchain connection timeout'));

      // Act
      const result = await exchangeRateHandlers.handleGetMVTWalletExchangeRate(mockEvent, mockArgs);

      // Assert
      expect(result.statusCode).toBe(500);
    });

    test('should handle malformed exchange rate responses', async () => {
      // Arrange
      const malformedData = {
        currentRate: 'invalid',
        liquidityStatus: null
      };
      
      exchangeRateService.getExchangeRateSummary.mockResolvedValue(malformedData);

      // Act
      const result = await exchangeRateHandlers.handleGetMVTWalletExchangeRate(mockEvent, mockArgs);

      // Assert
      expect(result.statusCode).toBe(200); // Should still return success but with malformed data
    });

    test('should handle concurrent rate requests', async () => {
      // Arrange
      const mockExchangeRateData = {
        currentRate: 0.5,
        rateDisplay: '1 MVT = 0.50 USDC',
        liquidityStatus: {
          status: 'AVAILABLE'
        }
      };
      
      exchangeRateService.getExchangeRateSummary.mockResolvedValue(mockExchangeRateData);
      responseUtils.createSuccessResponse.mockReturnValue({
        statusCode: 200,
        data: mockExchangeRateData
      });

      // Act
      const promises = Array(5).fill().map(() => 
        exchangeRateHandlers.handleGetMVTWalletExchangeRate(mockEvent, mockArgs)
      );
      
      const results = await Promise.all(promises);

      // Assert
      expect(results).toHaveLength(5);
      expect(results.every(result => result.statusCode === 200)).toBe(true);
      expect(exchangeRateService.getExchangeRateSummary).toHaveBeenCalledTimes(5);
    });

    test('should handle different liquidity status scenarios', async () => {
      // Test different status scenarios
      const statusScenarios = [
        {
          status: 'AVAILABLE',
          expectedStatusCode: 200,
          expectedMessage: 'Exchange rate retrieved successfully'
        },
        {
          status: 'FALLBACK_MODE',
          expectedStatusCode: 200,
          expectedMessage: 'Exchange rate retrieved successfully (using fallback values due to liquidity constraints)'
        },
        {
          status: 'UNAVAILABLE',
          expectedStatusCode: 500,
          expectedMessage: 'Exchange rates are currently unavailable. Please check system liquidity and try again.'
        }
      ];

      for (const scenario of statusScenarios) {
        // Arrange
        jest.clearAllMocks();
        const mockData = {
          currentRate: scenario.status === 'UNAVAILABLE' ? null : 0.5,
          liquidityStatus: { status: scenario.status }
        };
        
        exchangeRateService.getExchangeRateSummary.mockResolvedValue(mockData);
        
        if (scenario.expectedStatusCode === 200) {
          responseUtils.createSuccessResponse.mockReturnValue({
            statusCode: scenario.expectedStatusCode,
            message: scenario.expectedMessage,
            data: mockData
          });
        } else {
          responseUtils.createInternalErrorResponse.mockReturnValue({
            statusCode: scenario.expectedStatusCode,
            message: scenario.expectedMessage
          });
        }

        // Act
        const result = await exchangeRateHandlers.handleGetMVTWalletExchangeRate(mockEvent, mockArgs);

        // Assert
        expect(result.statusCode).toBe(scenario.expectedStatusCode);
        expect(result.message).toBe(scenario.expectedMessage);
      }
    });

    test('should handle rate calculation edge cases', async () => {
      // Arrange
      const edgeCaseData = {
        currentRate: 0.000001, // Very small rate
        rateDisplay: '1 MVT = 0.000001 USDC',
        liquidityStatus: {
          usdcReserves: 1.0,
          mvtSupply: 1000000.0,
          liquidityRatio: 0.000001,
          status: 'AVAILABLE'
        }
      };
      
      exchangeRateService.getExchangeRateSummary.mockResolvedValue(edgeCaseData);

      // Act
      const result = await exchangeRateHandlers.handleGetMVTWalletExchangeRate(mockEvent, mockArgs);

      // Assert
      expect(result.statusCode).toBe(200);
      expect(result.data.currentRate).toBe(0.000001);
    });

    test('should handle very large rate values', async () => {
      // Arrange
      const largeRateData = {
        currentRate: 1000000.0, // Very large rate
        rateDisplay: '1 MVT = 1000000.00 USDC',
        liquidityStatus: {
          usdcReserves: 1000000000.0,
          mvtSupply: 1000.0,
          liquidityRatio: 1000000.0,
          status: 'AVAILABLE'
        }
      };
      
      exchangeRateService.getExchangeRateSummary.mockResolvedValue(largeRateData);

      // Act
      const result = await exchangeRateHandlers.handleGetMVTWalletExchangeRate(mockEvent, mockArgs);

      // Assert
      expect(result.statusCode).toBe(200);
      expect(result.data.currentRate).toBe(1000000.0);
    });

    test('should handle zero liquidity scenarios', async () => {
      // Arrange
      const zeroLiquidityData = {
        currentRate: null,
        rateDisplay: 'No liquidity available',
        liquidityStatus: {
          usdcReserves: 0,
          mvtSupply: 0,
          liquidityRatio: 0,
          status: 'UNAVAILABLE'
        }
      };
      
      exchangeRateService.getExchangeRateSummary.mockResolvedValue(zeroLiquidityData);

      // Act
      const result = await exchangeRateHandlers.handleGetMVTWalletExchangeRate(mockEvent, mockArgs);

      // Assert
      expect(result.statusCode).toBe(500);
    });
  });

  describe('Error Handling and Resilience', () => {
    test('should handle timeout errors', async () => {
      // Arrange
      const mockEvent = global.testUtils.createMockEvent();
      const mockArgs = global.testUtils.createMockArgs();
      
      exchangeRateService.getExchangeRateSummary.mockRejectedValue(new Error('timeout'));

      // Act
      const result = await exchangeRateHandlers.handleGetMVTWalletExchangeRate(mockEvent, mockArgs);

      // Assert
      expect(result.statusCode).toBe(500);
    });

    test('should handle network errors', async () => {
      // Arrange
      const mockEvent = global.testUtils.createMockEvent();
      const mockArgs = global.testUtils.createMockArgs();
      
      exchangeRateService.getExchangeRateSummary.mockRejectedValue(new Error('network error'));

      // Act
      const result = await exchangeRateHandlers.handleGetMVTWalletExchangeRate(mockEvent, mockArgs);

      // Assert
      expect(result.statusCode).toBe(500);
    });

    test('should handle malformed event objects', async () => {
      // Arrange
      const malformedEvent = null;
      const mockArgs = global.testUtils.createMockArgs();

      // Act & Assert
      await expect(
        exchangeRateHandlers.handleGetMVTWalletExchangeRate(malformedEvent, mockArgs)
      ).rejects.toThrow();
    });
  });
});
