/**
 * Swap Service Test Suite
 * Tests for swap business logic, token locking/unlocking, and USDC transfers
 */

const swapService = require('../swap.service');
const walletService = require('../../wallet/wallet.service');
const usdcService = require('../../usdc/usdc.service');
const exchangeRateService = require('../../exchangeRate/exchangeRate.service');
const { mockDynamoDB } = require('../../../__tests__/setup');

// Mock dependencies
jest.mock('../../../config/aws', () => ({
  AWS: {
    DynamoDB: {
      Converter: {
        marshall: jest.fn((item) => item),
        unmarshall: jest.fn((item) => item)
      }
    }
  },
  ddb: mockDynamoDB
}));

jest.mock('../../wallet/wallet.service');
jest.mock('../../usdc/usdc.service');
jest.mock('../../exchangeRate/exchangeRate.service');
jest.mock('../../../shared/database/dynamoUtils', () => ({
  getTableName: jest.fn((tableName) => `test-${tableName}`),
  tableExists: jest.fn().mockResolvedValue(true)
}));

describe('Swap Service', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('createSwapRequest', () => {
    const userId = 'test-user-123';
    const mvtAmount = 100;
    const description = 'Test swap request';

    test('should create swap request successfully', async () => {
      // Arrange
      const mockUser = global.testUtils.createMockUser(userId, false);
      const mockValidation = {
        isValid: true,
        conversionData: {
          mvtAmount: 100,
          usdcAmount: 50.0,
          exchangeRate: 0.5
        }
      };
      
      mockDynamoDB.getItem.mockReturnValue(global.testUtils.mockDynamoDBSuccess({ Item: mockUser }));
      exchangeRateService.validateSwapFeasibility.mockResolvedValue(mockValidation);
      walletService.lockUserMVTTokens.mockResolvedValue(true);
      mockDynamoDB.putItem.mockReturnValue(global.testUtils.mockDynamoDBSuccess());

      // Act
      const result = await swapService.createSwapRequest(userId, mvtAmount, description);

      // Assert
      expect(exchangeRateService.validateSwapFeasibility).toHaveBeenCalledWith(mvtAmount);
      expect(walletService.lockUserMVTTokens).toHaveBeenCalledWith(userId, mvtAmount);
      expect(result).toMatchObject({
        userId: userId,
        mvtAmount: mvtAmount,
        usdcAmount: 50.0,
        exchangeRate: 0.5,
        status: 'PENDING',
        userWalletAddress: mockUser.walletAddress,
        description: description
      });
    });

    test('should validate user exists', async () => {
      // Arrange
      mockDynamoDB.getItem.mockReturnValue(global.testUtils.mockDynamoDBSuccess({}));

      // Act & Assert
      await expect(
        swapService.createSwapRequest(userId, mvtAmount, description)
      ).rejects.toThrow('User not found');
    });

    test('should validate user has wallet address', async () => {
      // Arrange
      const mockUserWithoutWallet = global.testUtils.createMockUser(userId, false);
      mockUserWithoutWallet.walletAddress = null;
      
      mockDynamoDB.getItem.mockReturnValue(global.testUtils.mockDynamoDBSuccess({ Item: mockUserWithoutWallet }));

      // Act & Assert
      await expect(
        swapService.createSwapRequest(userId, mvtAmount, description)
      ).rejects.toThrow('Please add a valid blockchain wallet address to your profile before requesting withdrawals');
    });

    test('should validate swap feasibility', async () => {
      // Arrange
      const mockUser = global.testUtils.createMockUser(userId, false);
      const mockValidation = {
        isValid: false,
        error: 'Insufficient USDC liquidity'
      };
      
      mockDynamoDB.getItem.mockReturnValue(global.testUtils.mockDynamoDBSuccess({ Item: mockUser }));
      exchangeRateService.validateSwapFeasibility.mockResolvedValue(mockValidation);

      // Act & Assert
      await expect(
        swapService.createSwapRequest(userId, mvtAmount, description)
      ).rejects.toThrow('Insufficient USDC liquidity');
    });

    test('should handle token locking failures', async () => {
      // Arrange
      const mockUser = global.testUtils.createMockUser(userId, false);
      const mockValidation = {
        isValid: true,
        conversionData: { mvtAmount: 100, usdcAmount: 50.0, exchangeRate: 0.5 }
      };
      
      mockDynamoDB.getItem.mockReturnValue(global.testUtils.mockDynamoDBSuccess({ Item: mockUser }));
      exchangeRateService.validateSwapFeasibility.mockResolvedValue(mockValidation);
      walletService.lockUserMVTTokens.mockRejectedValue(new Error('Insufficient balance'));

      // Act & Assert
      await expect(
        swapService.createSwapRequest(userId, mvtAmount, description)
      ).rejects.toThrow('Failed to create swap request');
    });

    test('should validate input parameters', async () => {
      // Act & Assert
      await expect(swapService.createSwapRequest('', 100, 'test')).rejects.toThrow('User ID is required');
      await expect(swapService.createSwapRequest(userId, 0, 'test')).rejects.toThrow('MVT amount must be greater than 0');
      await expect(swapService.createSwapRequest(userId, 1.5, 'test')).rejects.toThrow('MVT amount must be an integer');
      await expect(swapService.createSwapRequest(userId, 100, '')).rejects.toThrow('Description is required');
    });

    test('should generate unique swap request IDs', async () => {
      // Arrange
      const mockUser = global.testUtils.createMockUser(userId, false);
      const mockValidation = {
        isValid: true,
        conversionData: { mvtAmount: 100, usdcAmount: 50.0, exchangeRate: 0.5 }
      };
      
      mockDynamoDB.getItem.mockReturnValue(global.testUtils.mockDynamoDBSuccess({ Item: mockUser }));
      exchangeRateService.validateSwapFeasibility.mockResolvedValue(mockValidation);
      walletService.lockUserMVTTokens.mockResolvedValue(true);
      mockDynamoDB.putItem.mockReturnValue(global.testUtils.mockDynamoDBSuccess());

      // Act
      const [result1, result2] = await Promise.all([
        swapService.createSwapRequest(userId, 100, 'Swap 1'),
        swapService.createSwapRequest(userId, 200, 'Swap 2')
      ]);

      // Assert
      expect(result1.id).not.toBe(result2.id);
      expect(result1.id).toMatch(/^swap-/);
      expect(result2.id).toMatch(/^swap-/);
    });
  });

  describe('approveSwapRequest', () => {
    const swapRequestId = 'swap-123';
    const adminUserId = 'admin-user-123';

    test('should approve swap request successfully', async () => {
      // Arrange
      const mockSwapRequest = global.testUtils.createMockSwapRequest(swapRequestId, 'PENDING');
      const mockTransferResult = {
        hash: '0xabcdef123456',
        blockNumber: 12345,
        gasUsed: '25000',
        status: 1
      };
      
      mockDynamoDB.getItem.mockReturnValue(global.testUtils.mockDynamoDBSuccess({ Item: mockSwapRequest }));
      usdcService.transferUSDCToUser.mockResolvedValue(mockTransferResult);
      walletService.transferLockedMVTToCentral.mockResolvedValue(true);
      mockDynamoDB.updateItem.mockReturnValue(global.testUtils.mockDynamoDBSuccess());

      // Act
      const result = await swapService.approveSwapRequest(swapRequestId, adminUserId);

      // Assert
      expect(usdcService.transferUSDCToUser).toHaveBeenCalledWith(
        mockSwapRequest.userWalletAddress,
        mockSwapRequest.usdcAmount
      );
      expect(walletService.transferLockedMVTToCentral).toHaveBeenCalledWith(
        mockSwapRequest.userId,
        mockSwapRequest.mvtAmount
      );
      expect(result.status).toBe('APPROVED');
      expect(result.transactionHash).toBe('0xabcdef123456');
      expect(result.adminUserId).toBe(adminUserId);
    });

    test('should validate swap request exists', async () => {
      // Arrange
      mockDynamoDB.getItem.mockReturnValue(global.testUtils.mockDynamoDBSuccess({}));

      // Act & Assert
      await expect(
        swapService.approveSwapRequest(swapRequestId, adminUserId)
      ).rejects.toThrow('Swap request not found');
    });

    test('should validate swap request is pending', async () => {
      // Arrange
      const mockSwapRequest = global.testUtils.createMockSwapRequest(swapRequestId, 'APPROVED');
      mockDynamoDB.getItem.mockReturnValue(global.testUtils.mockDynamoDBSuccess({ Item: mockSwapRequest }));

      // Act & Assert
      await expect(
        swapService.approveSwapRequest(swapRequestId, adminUserId)
      ).rejects.toThrow('Swap request already processed');
    });

    test('should handle USDC transfer failures', async () => {
      // Arrange
      const mockSwapRequest = global.testUtils.createMockSwapRequest(swapRequestId, 'PENDING');
      
      mockDynamoDB.getItem.mockReturnValue(global.testUtils.mockDynamoDBSuccess({ Item: mockSwapRequest }));
      usdcService.transferUSDCToUser.mockRejectedValue(new Error('Insufficient USDC balance'));

      // Act & Assert
      await expect(
        swapService.approveSwapRequest(swapRequestId, adminUserId)
      ).rejects.toThrow('Failed to approve swap request');
    });

    test('should rollback on MVT transfer failure', async () => {
      // Arrange
      const mockSwapRequest = global.testUtils.createMockSwapRequest(swapRequestId, 'PENDING');
      const mockTransferResult = { hash: '0xabcdef123456', status: 1 };
      
      mockDynamoDB.getItem.mockReturnValue(global.testUtils.mockDynamoDBSuccess({ Item: mockSwapRequest }));
      usdcService.transferUSDCToUser.mockResolvedValue(mockTransferResult);
      walletService.transferLockedMVTToCentral.mockRejectedValue(new Error('MVT transfer failed'));

      // Act & Assert
      await expect(
        swapService.approveSwapRequest(swapRequestId, adminUserId)
      ).rejects.toThrow('Failed to approve swap request');
    });

    test('should validate input parameters', async () => {
      // Act & Assert
      await expect(swapService.approveSwapRequest('', adminUserId)).rejects.toThrow('Swap request ID is required');
      await expect(swapService.approveSwapRequest(swapRequestId, '')).rejects.toThrow('Admin user ID is required');
    });
  });

  describe('rejectSwapRequest', () => {
    const swapRequestId = 'swap-123';
    const adminUserId = 'admin-user-123';
    const reason = 'Insufficient documentation';

    test('should reject swap request successfully', async () => {
      // Arrange
      const mockSwapRequest = global.testUtils.createMockSwapRequest(swapRequestId, 'PENDING');
      
      mockDynamoDB.getItem.mockReturnValue(global.testUtils.mockDynamoDBSuccess({ Item: mockSwapRequest }));
      walletService.unlockUserMVTTokens.mockResolvedValue(true);
      mockDynamoDB.updateItem.mockReturnValue(global.testUtils.mockDynamoDBSuccess());

      // Act
      const result = await swapService.rejectSwapRequest(swapRequestId, adminUserId, reason);

      // Assert
      expect(walletService.unlockUserMVTTokens).toHaveBeenCalledWith(
        mockSwapRequest.userId,
        mockSwapRequest.mvtAmount
      );
      expect(result.status).toBe('REJECTED');
      expect(result.rejectionReason).toBe(reason);
      expect(result.adminUserId).toBe(adminUserId);
    });

    test('should validate swap request exists', async () => {
      // Arrange
      mockDynamoDB.getItem.mockReturnValue(global.testUtils.mockDynamoDBSuccess({}));

      // Act & Assert
      await expect(
        swapService.rejectSwapRequest(swapRequestId, adminUserId, reason)
      ).rejects.toThrow('Swap request not found');
    });

    test('should validate swap request is pending', async () => {
      // Arrange
      const mockSwapRequest = global.testUtils.createMockSwapRequest(swapRequestId, 'REJECTED');
      mockDynamoDB.getItem.mockReturnValue(global.testUtils.mockDynamoDBSuccess({ Item: mockSwapRequest }));

      // Act & Assert
      await expect(
        swapService.rejectSwapRequest(swapRequestId, adminUserId, reason)
      ).rejects.toThrow('Swap request already processed');
    });

    test('should handle token unlock failures', async () => {
      // Arrange
      const mockSwapRequest = global.testUtils.createMockSwapRequest(swapRequestId, 'PENDING');
      
      mockDynamoDB.getItem.mockReturnValue(global.testUtils.mockDynamoDBSuccess({ Item: mockSwapRequest }));
      walletService.unlockUserMVTTokens.mockRejectedValue(new Error('Token unlock failed'));

      // Act & Assert
      await expect(
        swapService.rejectSwapRequest(swapRequestId, adminUserId, reason)
      ).rejects.toThrow('Failed to reject swap request');
    });

    test('should validate input parameters', async () => {
      // Act & Assert
      await expect(swapService.rejectSwapRequest('', adminUserId, reason)).rejects.toThrow('Swap request ID is required');
      await expect(swapService.rejectSwapRequest(swapRequestId, '', reason)).rejects.toThrow('Admin user ID is required');
      await expect(swapService.rejectSwapRequest(swapRequestId, adminUserId, '')).rejects.toThrow('Rejection reason is required');
    });
  });

  describe('getSwapRequests', () => {
    test('should retrieve user swap requests', async () => {
      // Arrange
      const userId = 'test-user-123';
      const mockSwapRequests = {
        Items: [
          global.testUtils.createMockSwapRequest('swap-1', 'PENDING'),
          global.testUtils.createMockSwapRequest('swap-2', 'APPROVED')
        ]
      };
      
      mockDynamoDB.scan.mockReturnValue(global.testUtils.mockDynamoDBSuccess(mockSwapRequests));

      // Act
      const result = await swapService.getSwapRequests(userId, false);

      // Assert
      expect(mockDynamoDB.scan).toHaveBeenCalledWith(
        expect.objectContaining({
          TableName: 'test-MVTWalletSwapRequest',
          FilterExpression: 'userId = :userId AND isDeleted = :isDeleted',
          ExpressionAttributeValues: {
            ':userId': userId,
            ':isDeleted': 'false'
          }
        })
      );
      expect(result).toHaveLength(2);
    });

    test('should retrieve all swap requests for admin', async () => {
      // Arrange
      const mockSwapRequests = {
        Items: [
          global.testUtils.createMockSwapRequest('swap-1', 'PENDING'),
          global.testUtils.createMockSwapRequest('swap-2', 'APPROVED'),
          global.testUtils.createMockSwapRequest('swap-3', 'REJECTED')
        ]
      };
      
      mockDynamoDB.scan.mockReturnValue(global.testUtils.mockDynamoDBSuccess(mockSwapRequests));

      // Act
      const result = await swapService.getSwapRequests(null, true);

      // Assert
      expect(mockDynamoDB.scan).toHaveBeenCalledWith(
        expect.objectContaining({
          FilterExpression: 'isDeleted = :isDeleted',
          ExpressionAttributeValues: {
            ':isDeleted': 'false'
          }
        })
      );
      expect(result).toHaveLength(3);
    });

    test('should handle empty results', async () => {
      // Arrange
      mockDynamoDB.scan.mockReturnValue(global.testUtils.mockDynamoDBSuccess({ Items: [] }));

      // Act
      const result = await swapService.getSwapRequests('user-123', false);

      // Assert
      expect(result).toEqual([]);
    });

    test('should enrich swap requests with user data', async () => {
      // Arrange
      const mockSwapRequests = {
        Items: [
          global.testUtils.createMockSwapRequest('swap-1', 'PENDING')
        ]
      };
      const mockUser = global.testUtils.createMockUser('test-user-123', false);
      
      mockDynamoDB.scan.mockReturnValue(global.testUtils.mockDynamoDBSuccess(mockSwapRequests));
      mockDynamoDB.batchGetItem.mockReturnValue(global.testUtils.mockDynamoDBSuccess({
        Responses: {
          'test-Person': [mockUser]
        }
      }));

      // Act
      const result = await swapService.getSwapRequests(null, true);

      // Assert
      expect(result[0].user).toEqual(mockUser);
    });

    test('should handle user data enrichment failures gracefully', async () => {
      // Arrange
      const mockSwapRequests = {
        Items: [
          global.testUtils.createMockSwapRequest('swap-1', 'PENDING')
        ]
      };
      
      mockDynamoDB.scan.mockReturnValue(global.testUtils.mockDynamoDBSuccess(mockSwapRequests));
      mockDynamoDB.batchGetItem.mockReturnValue(global.testUtils.mockDynamoDBError(new Error('Batch get failed')));

      // Act
      const result = await swapService.getSwapRequests(null, true);

      // Assert
      expect(result).toHaveLength(1);
      expect(result[0].user).toBeUndefined();
    });
  });

  describe('Race Condition Prevention', () => {
    test('should handle concurrent swap requests safely', async () => {
      // Arrange
      const userId = 'test-user-123';
      const mockUser = global.testUtils.createMockUser(userId, false);
      const mockValidation = {
        isValid: true,
        conversionData: { mvtAmount: 100, usdcAmount: 50.0, exchangeRate: 0.5 }
      };
      
      mockDynamoDB.getItem.mockReturnValue(global.testUtils.mockDynamoDBSuccess({ Item: mockUser }));
      exchangeRateService.validateSwapFeasibility.mockResolvedValue(mockValidation);
      walletService.lockUserMVTTokens.mockResolvedValue(true);
      mockDynamoDB.putItem.mockReturnValue(global.testUtils.mockDynamoDBSuccess());

      // Act
      const promises = Array(3).fill().map((_, i) => 
        swapService.createSwapRequest(userId, 100, `Swap ${i + 1}`)
      );
      
      const results = await Promise.all(promises);

      // Assert
      expect(results).toHaveLength(3);
      expect(results.every(result => result.status === 'PENDING')).toBe(true);
      expect(walletService.lockUserMVTTokens).toHaveBeenCalledTimes(3);
    });

    test('should handle concurrent admin operations on different swaps', async () => {
      // Arrange
      const adminUserId = 'admin-user-123';
      const mockSwap1 = global.testUtils.createMockSwapRequest('swap-1', 'PENDING');
      const mockSwap2 = global.testUtils.createMockSwapRequest('swap-2', 'PENDING');
      
      mockDynamoDB.getItem
        .mockReturnValueOnce(global.testUtils.mockDynamoDBSuccess({ Item: mockSwap1 }))
        .mockReturnValueOnce(global.testUtils.mockDynamoDBSuccess({ Item: mockSwap2 }));
      
      usdcService.transferUSDCToUser.mockResolvedValue({ hash: '0xabcdef', status: 1 });
      walletService.transferLockedMVTToCentral.mockResolvedValue(true);
      walletService.unlockUserMVTTokens.mockResolvedValue(true);
      mockDynamoDB.updateItem.mockReturnValue(global.testUtils.mockDynamoDBSuccess());

      // Act
      const [approveResult, rejectResult] = await Promise.all([
        swapService.approveSwapRequest('swap-1', adminUserId),
        swapService.rejectSwapRequest('swap-2', adminUserId, 'Test rejection')
      ]);

      // Assert
      expect(approveResult.status).toBe('APPROVED');
      expect(rejectResult.status).toBe('REJECTED');
    });
  });

  describe('Data Integrity', () => {
    test('should maintain transaction atomicity during approval', async () => {
      // Arrange
      const swapRequestId = 'swap-123';
      const adminUserId = 'admin-user-123';
      const mockSwapRequest = global.testUtils.createMockSwapRequest(swapRequestId, 'PENDING');
      
      mockDynamoDB.getItem.mockReturnValue(global.testUtils.mockDynamoDBSuccess({ Item: mockSwapRequest }));
      usdcService.transferUSDCToUser.mockResolvedValue({ hash: '0xabcdef', status: 1 });
      walletService.transferLockedMVTToCentral.mockResolvedValue(true);
      
      // Simulate database update failure
      mockDynamoDB.updateItem.mockReturnValue(global.testUtils.mockDynamoDBError(new Error('Update failed')));

      // Act & Assert
      await expect(
        swapService.approveSwapRequest(swapRequestId, adminUserId)
      ).rejects.toThrow('Failed to approve swap request');
    });

    test('should maintain audit trail completeness', async () => {
      // Arrange
      const swapRequestId = 'swap-123';
      const adminUserId = 'admin-user-123';
      const mockSwapRequest = global.testUtils.createMockSwapRequest(swapRequestId, 'PENDING');
      
      mockDynamoDB.getItem.mockReturnValue(global.testUtils.mockDynamoDBSuccess({ Item: mockSwapRequest }));
      usdcService.transferUSDCToUser.mockResolvedValue({
        hash: '0xabcdef123456',
        blockNumber: 12345,
        gasUsed: '25000',
        status: 1
      });
      walletService.transferLockedMVTToCentral.mockResolvedValue(true);
      mockDynamoDB.updateItem.mockReturnValue(global.testUtils.mockDynamoDBSuccess());

      // Act
      const result = await swapService.approveSwapRequest(swapRequestId, adminUserId);

      // Assert
      expect(mockDynamoDB.updateItem).toHaveBeenCalledWith(
        expect.objectContaining({
          UpdateExpression: expect.stringContaining('SET #status = :status'),
          ExpressionAttributeValues: expect.objectContaining({
            ':status': 'APPROVED',
            ':adminUserId': adminUserId,
            ':transactionHash': '0xabcdef123456'
          })
        })
      );
      expect(result.processedAt).toBeDefined();
    });
  });
});
