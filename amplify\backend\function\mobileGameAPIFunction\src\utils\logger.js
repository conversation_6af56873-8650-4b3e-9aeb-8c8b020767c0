/**
 * Shared Logging Utility for AWS Lambda Functions
 * 
 * This module provides a structured logging solution using Pino,
 * optimized for AWS Lambda environments with CloudWatch integration.
 */

const pino = require('pino');

const getLogLevel = () => {
  if (process.env.AWS_LAMBDA_LOG_LEVEL) {
    return process.env.AWS_LAMBDA_LOG_LEVEL.toLowerCase();
  }
  
  const env = process.env.ENV || process.env.NODE_ENV || 'development';
  
  switch (env.toLowerCase()) {
    case 'prod':
    case 'production':
      return 'warn';
    case 'dev':
    case 'development':
      return 'debug';
    case 'amplifydev':
    case 'staging':
      return 'info';
    default:
      return 'info';
  }
};

const pinoConfig = {
  level: getLogLevel(),
  formatters: {
    bindings: (bindings) => {
      return {
        nodeVersion: process.version,
        functionName: process.env.AWS_LAMBDA_FUNCTION_NAME || 'unknown',
        functionVersion: process.env.AWS_LAMBDA_FUNCTION_VERSION || '$LATEST',
        environment: process.env.ENV || 'unknown'
      };
    },
    level: (label) => {
      return { level: label.toUpperCase() };
    }
  },
  timestamp: () => `,"timestamp":"${new Date().toISOString()}"`,
  prettyPrint: false,
  sync: true
};

const baseLogger = pino(pinoConfig);

function createLogger(context = {}, additionalFields = {}) {
  const contextFields = {
    requestId: context.awsRequestId || 'unknown',
    remainingTimeMs: context.getRemainingTimeInMillis ? context.getRemainingTimeInMillis() : undefined,
    ...additionalFields
  };
  
  Object.keys(contextFields).forEach(key => {
    if (contextFields[key] === undefined) {
      delete contextFields[key];
    }
  });
  
  return baseLogger.child(contextFields);
}

function logPerformance(logger, operation, startTime, additionalMetrics = {}) {
  const duration = Date.now() - startTime;
  
  logger.info({
    operation,
    duration,
    performanceMetrics: {
      durationMs: duration,
      ...additionalMetrics
    }
  }, `Performance: ${operation} completed in ${duration}ms`);
}

function logError(logger, error, operation = 'unknown', context = {}) {
  logger.error({
    error: {
      name: error.name,
      message: error.message,
      stack: error.stack,
      code: error.code
    },
    operation,
    errorContext: context
  }, `Error in ${operation}: ${error.message}`);
}

function logSuccess(logger, operation, result = {}, summary = {}) {
  logger.info({
    operation,
    success: true,
    resultSummary: summary
  }, `Successfully completed ${operation}`);
}

module.exports = {
  createLogger,
  logPerformance,
  logError,
  logSuccess,
  baseLogger
};
