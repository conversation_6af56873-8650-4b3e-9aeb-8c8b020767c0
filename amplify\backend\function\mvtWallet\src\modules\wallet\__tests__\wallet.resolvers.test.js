/**
 * Wallet Resolvers Test Suite
 * Tests for GraphQL field mapping and function routing
 */

const walletResolvers = require('../wallet.resolvers');
const walletHandlers = require('../wallet.handlers');

// Mock wallet handlers
jest.mock('../wallet.handlers', () => ({
  handleGetAdminMVTWalletBalance: jest.fn(),
  handleGetUserMVTWalletBalance: jest.fn()
}));

describe('Wallet Resolvers', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('Resolver Structure', () => {
    test('should export all required resolver functions', () => {
      expect(typeof walletResolvers.getAdminMVTWalletBalance).toBe('function');
      expect(typeof walletResolvers.getUserMVTWalletBalance).toBe('function');
    });

    test('should map getAdminMVTWalletBalance to correct handler', () => {
      expect(walletResolvers.getAdminMVTWalletBalance).toBe(walletHandlers.handleGetAdminMVTWalletBalance);
    });

    test('should map getUserMVTWalletBalance to correct handler', () => {
      expect(walletResolvers.getUserMVTWalletBalance).toBe(walletHandlers.handleGetUserMVTWalletBalance);
    });

    test('should not export any unexpected functions', () => {
      const expectedKeys = ['getAdminMVTWalletBalance', 'getUserMVTWalletBalance'];
      const actualKeys = Object.keys(walletResolvers);
      
      expect(actualKeys.sort()).toEqual(expectedKeys.sort());
    });
  });

  describe('Resolver Function Calls', () => {
    test('should call admin balance handler with correct parameters', async () => {
      // Arrange
      const mockEvent = global.testUtils.createMockEvent('admin-cognito-id', true);
      const mockArgs = global.testUtils.createMockArgs();
      const mockResponse = {
        statusCode: 200,
        message: 'Success',
        data: global.testUtils.createMockWalletBalance(5000, 0)
      };
      
      walletHandlers.handleGetAdminMVTWalletBalance.mockResolvedValue(mockResponse);

      // Act
      const result = await walletResolvers.getAdminMVTWalletBalance(mockEvent, mockArgs);

      // Assert
      expect(walletHandlers.handleGetAdminMVTWalletBalance).toHaveBeenCalledWith(mockEvent, mockArgs);
      expect(result).toEqual(mockResponse);
    });

    test('should call user balance handler with correct parameters', async () => {
      // Arrange
      const mockEvent = global.testUtils.createMockEvent('user-cognito-id', false);
      const mockArgs = global.testUtils.createMockArgs();
      const mockResponse = {
        statusCode: 200,
        message: 'Success',
        data: global.testUtils.createMockWalletBalance(1000, 100)
      };
      
      walletHandlers.handleGetUserMVTWalletBalance.mockResolvedValue(mockResponse);

      // Act
      const result = await walletResolvers.getUserMVTWalletBalance(mockEvent, mockArgs);

      // Assert
      expect(walletHandlers.handleGetUserMVTWalletBalance).toHaveBeenCalledWith(mockEvent, mockArgs);
      expect(result).toEqual(mockResponse);
    });

    test('should propagate handler errors', async () => {
      // Arrange
      const mockEvent = global.testUtils.createMockEvent();
      const mockArgs = global.testUtils.createMockArgs();
      const mockError = new Error('Handler error');
      
      walletHandlers.handleGetAdminMVTWalletBalance.mockRejectedValue(mockError);

      // Act & Assert
      await expect(
        walletResolvers.getAdminMVTWalletBalance(mockEvent, mockArgs)
      ).rejects.toThrow('Handler error');
    });

    test('should handle undefined event gracefully', async () => {
      // Arrange
      const mockArgs = global.testUtils.createMockArgs();
      walletHandlers.handleGetUserMVTWalletBalance.mockResolvedValue({
        statusCode: 400,
        message: 'Invalid request'
      });

      // Act
      const result = await walletResolvers.getUserMVTWalletBalance(undefined, mockArgs);

      // Assert
      expect(walletHandlers.handleGetUserMVTWalletBalance).toHaveBeenCalledWith(undefined, mockArgs);
      expect(result.statusCode).toBe(400);
    });

    test('should handle undefined args gracefully', async () => {
      // Arrange
      const mockEvent = global.testUtils.createMockEvent();
      walletHandlers.handleGetAdminMVTWalletBalance.mockResolvedValue({
        statusCode: 400,
        message: 'Invalid request'
      });

      // Act
      const result = await walletResolvers.getAdminMVTWalletBalance(mockEvent, undefined);

      // Assert
      expect(walletHandlers.handleGetAdminMVTWalletBalance).toHaveBeenCalledWith(mockEvent, undefined);
      expect(result.statusCode).toBe(400);
    });
  });

  describe('Resolver Integration', () => {
    test('should maintain function signatures for GraphQL compatibility', () => {
      // Test that resolvers accept the standard GraphQL resolver signature
      const mockParent = {};
      const mockArgs = global.testUtils.createMockArgs();
      const mockContext = {};
      const mockInfo = {};

      // These should not throw errors when called with GraphQL resolver signature
      expect(() => {
        walletResolvers.getAdminMVTWalletBalance(mockParent, mockArgs, mockContext, mockInfo);
      }).not.toThrow();

      expect(() => {
        walletResolvers.getUserMVTWalletBalance(mockParent, mockArgs, mockContext, mockInfo);
      }).not.toThrow();
    });

    test('should handle concurrent resolver calls', async () => {
      // Arrange
      const mockEvent1 = global.testUtils.createMockEvent('admin-1', true);
      const mockEvent2 = global.testUtils.createMockEvent('user-1', false);
      const mockArgs = global.testUtils.createMockArgs();

      walletHandlers.handleGetAdminMVTWalletBalance.mockResolvedValue({
        statusCode: 200,
        data: global.testUtils.createMockWalletBalance(5000, 0)
      });
      
      walletHandlers.handleGetUserMVTWalletBalance.mockResolvedValue({
        statusCode: 200,
        data: global.testUtils.createMockWalletBalance(1000, 100)
      });

      // Act
      const [adminResult, userResult] = await Promise.all([
        walletResolvers.getAdminMVTWalletBalance(mockEvent1, mockArgs),
        walletResolvers.getUserMVTWalletBalance(mockEvent2, mockArgs)
      ]);

      // Assert
      expect(adminResult.statusCode).toBe(200);
      expect(userResult.statusCode).toBe(200);
      expect(walletHandlers.handleGetAdminMVTWalletBalance).toHaveBeenCalledWith(mockEvent1, mockArgs);
      expect(walletHandlers.handleGetUserMVTWalletBalance).toHaveBeenCalledWith(mockEvent2, mockArgs);
    });
  });

  describe('Error Handling', () => {
    test('should handle handler timeout errors', async () => {
      // Arrange
      const mockEvent = global.testUtils.createMockEvent();
      const mockArgs = global.testUtils.createMockArgs();
      const timeoutError = new Error('timeout');
      
      walletHandlers.handleGetAdminMVTWalletBalance.mockRejectedValue(timeoutError);

      // Act & Assert
      await expect(
        walletResolvers.getAdminMVTWalletBalance(mockEvent, mockArgs)
      ).rejects.toThrow('timeout');
    });

    test('should handle handler network errors', async () => {
      // Arrange
      const mockEvent = global.testUtils.createMockEvent();
      const mockArgs = global.testUtils.createMockArgs();
      const networkError = new Error('network error');
      
      walletHandlers.handleGetUserMVTWalletBalance.mockRejectedValue(networkError);

      // Act & Assert
      await expect(
        walletResolvers.getUserMVTWalletBalance(mockEvent, mockArgs)
      ).rejects.toThrow('network error');
    });

    test('should handle handler validation errors', async () => {
      // Arrange
      const mockEvent = global.testUtils.createMockEvent();
      const mockArgs = global.testUtils.createMockArgs();
      const validationError = new Error('validation failed');
      
      walletHandlers.handleGetAdminMVTWalletBalance.mockRejectedValue(validationError);

      // Act & Assert
      await expect(
        walletResolvers.getAdminMVTWalletBalance(mockEvent, mockArgs)
      ).rejects.toThrow('validation failed');
    });
  });

  describe('Performance and Memory', () => {
    test('should not create new function instances on each call', () => {
      // Arrange & Act
      const resolver1 = walletResolvers.getAdminMVTWalletBalance;
      const resolver2 = walletResolvers.getAdminMVTWalletBalance;

      // Assert
      expect(resolver1).toBe(resolver2);
      expect(resolver1).toBe(walletHandlers.handleGetAdminMVTWalletBalance);
    });

    test('should handle rapid successive calls', async () => {
      // Arrange
      const mockEvent = global.testUtils.createMockEvent();
      const mockArgs = global.testUtils.createMockArgs();
      const mockResponse = { statusCode: 200, data: {} };
      
      walletHandlers.handleGetUserMVTWalletBalance.mockResolvedValue(mockResponse);

      // Act
      const promises = Array(10).fill().map(() => 
        walletResolvers.getUserMVTWalletBalance(mockEvent, mockArgs)
      );
      
      const results = await Promise.all(promises);

      // Assert
      expect(results).toHaveLength(10);
      expect(results.every(result => result.statusCode === 200)).toBe(true);
      expect(walletHandlers.handleGetUserMVTWalletBalance).toHaveBeenCalledTimes(10);
    });
  });
});
