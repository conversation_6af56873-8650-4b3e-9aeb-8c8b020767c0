/**
 * DynamoDB utility functions
 */

const { createDatabaseLogger } = require('../utils/logger');

/**
 * Get table name with environment prefix
 * @param {string} tableName - Base table name
 * @returns {string} - Full table name with environment prefix
 */
function getTableName(tableName) {
  // Use the same naming convention as the legacy system
  const fullTableName = `${tableName}-${process.env.API_MYVILLAGEPROJECTADMI_GRAPHQLAPIIDOUTPUT}-${process.env.ENV}`;

  // Only log in debug mode to avoid cluttering logs
  if (process.env.AWS_LAMBDA_LOG_LEVEL === 'DEBUG') {
    const logger = createDatabaseLogger({}, 'getTableName', tableName);
    logger.debug({
      baseTableName: tableName,
      fullTableName,
      apiId: process.env.API_MYVILLAGEPROJECTADMI_GRAPHQLAPIIDOUTPUT,
      environment: process.env.ENV
    }, `Generated table name: ${fullTableName}`);
  }

  return fullTableName;
}

/**
 * Check if a DynamoDB table exists
 * @param {object} ddb - DynamoDB client instance
 * @param {string} tableName - Table name to check
 * @returns {Promise<boolean>} - True if table exists
 */
async function tableExists(ddb, tableName) {
  try {
    await ddb.describeTable({ TableName: tableName }).promise();
    return true;
  } catch (error) {
    if (error.code === 'ResourceNotFoundException') {
      return false;
    }
    throw error;
  }
}

/**
 * Create a DynamoDB condition expression for version-based optimistic locking
 * @param {number} expectedVersion - Expected version number
 * @returns {object} - Condition expression object
 */
function createVersionCondition(expectedVersion) {
  return {
    ConditionExpression: "#version = :expectedVersion",
    ExpressionAttributeNames: {
      "#version": "version"
    },
    ExpressionAttributeValues: {
      ":expectedVersion": { N: expectedVersion.toString() }
    }
  };
}

/**
 * Create a DynamoDB update expression for incrementing version
 * @param {object} existingExpression - Existing update expression object
 * @returns {object} - Updated expression object with version increment
 */
function addVersionIncrement(existingExpression) {
  const expression = { ...existingExpression };
  
  // Add version increment to update expression
  if (expression.UpdateExpression) {
    expression.UpdateExpression += ", #version = if_not_exists(#version, :zero) + :one";
  } else {
    expression.UpdateExpression = "SET #version = if_not_exists(#version, :zero) + :one";
  }
  
  // Add expression attribute names
  expression.ExpressionAttributeNames = {
    ...expression.ExpressionAttributeNames,
    "#version": "version"
  };
  
  // Add expression attribute values
  expression.ExpressionAttributeValues = {
    ...expression.ExpressionAttributeValues,
    ":zero": { N: "0" },
    ":one": { N: "1" }
  };
  
  return expression;
}

/**
 * Create a safe update expression that handles missing fields
 * @param {object} updates - Object with field names and values to update
 * @param {object} options - Options for the update
 * @returns {object} - DynamoDB update expression object
 */
function createSafeUpdateExpression(updates, options = {}) {
  const { incrementVersion = true, timestamp = true } = options;
  
  const updateExpressions = [];
  const expressionAttributeNames = {};
  const expressionAttributeValues = {};
  
  // Add field updates
  Object.entries(updates).forEach(([field, value]) => {
    const fieldName = `#${field}`;
    const valueName = `:${field}`;
    
    updateExpressions.push(`${fieldName} = ${valueName}`);
    expressionAttributeNames[fieldName] = field;
    
    // Handle different value types
    if (typeof value === 'number') {
      expressionAttributeValues[valueName] = { N: value.toString() };
    } else if (typeof value === 'string') {
      expressionAttributeValues[valueName] = { S: value };
    } else if (typeof value === 'boolean') {
      expressionAttributeValues[valueName] = { BOOL: value };
    } else {
      expressionAttributeValues[valueName] = { S: JSON.stringify(value) };
    }
  });
  
  // Add timestamp if requested
  if (timestamp) {
    const now = new Date().toISOString();
    updateExpressions.push("#lastUpdated = :lastUpdated");
    expressionAttributeNames["#lastUpdated"] = "lastUpdated";
    expressionAttributeValues[":lastUpdated"] = { S: now };
  }
  
  // Add version increment if requested
  if (incrementVersion) {
    updateExpressions.push("#version = if_not_exists(#version, :zero) + :one");
    expressionAttributeNames["#version"] = "version";
    expressionAttributeValues[":zero"] = { N: "0" };
    expressionAttributeValues[":one"] = { N: "1" };
  }
  
  return {
    UpdateExpression: `SET ${updateExpressions.join(", ")}`,
    ExpressionAttributeNames: expressionAttributeNames,
    ExpressionAttributeValues: expressionAttributeValues
  };
}

/**
 * Create a conditional expression for checking if a record exists
 * @param {boolean} shouldExist - True if record should exist, false if it shouldn't
 * @returns {string} - Condition expression
 */
function createExistenceCondition(shouldExist = false) {
  return shouldExist ? "attribute_exists(id)" : "attribute_not_exists(id)";
}

/**
 * Retry a DynamoDB operation with exponential backoff
 * @param {function} operation - Function that returns a promise
 * @param {number} maxRetries - Maximum number of retries
 * @param {number} baseDelay - Base delay in milliseconds
 * @returns {Promise<any>} - Result of the operation
 */
async function retryOperation(operation, maxRetries = 3, baseDelay = 100) {
  let lastError;
  
  for (let attempt = 0; attempt <= maxRetries; attempt++) {
    try {
      return await operation();
    } catch (error) {
      lastError = error;
      
      // Don't retry on certain error types
      if (error.code === 'ValidationException' || 
          error.code === 'ResourceNotFoundException' ||
          attempt === maxRetries) {
        throw error;
      }
      
      // Only retry on conditional check failures and throttling
      if (error.code === 'ConditionalCheckFailedException' || 
          error.code === 'ProvisionedThroughputExceededException') {
        
        const delay = baseDelay * Math.pow(2, attempt) + Math.random() * 100;
        await new Promise(resolve => setTimeout(resolve, delay));
        continue;
      }
      
      // Don't retry other error types
      throw error;
    }
  }
  
  throw lastError;
}

module.exports = {
  getTableName,
  tableExists,
  createVersionCondition,
  addVersionIncrement,
  createSafeUpdateExpression,
  createExistenceCondition,
  retryOperation
};
