const axios = require('axios');
const transactionService = require('../transaction/transaction.service');

// Initialize Stripe with API key from environment variables
let stripe;
try {
  if (!process.env.STRIPE_SECRET_KEY) {
    console.error("STRIPE_SECRET_KEY environment variable is not set");
    // Initialize without a key for now, will fail gracefully when called
    stripe = require('stripe')();
  } else {
    console.log("Initializing Stripe with API key");
    // Specify the API version that supports Crypto Onramp
    stripe = require('stripe')(process.env.STRIPE_SECRET_KEY, {
      apiVersion: '2023-10-16', // Use the API version that supports Crypto Onramp
      appInfo: {
        name: 'MyVillage Token App',
        version: '1.0.0'
      }
    });
  }
} catch (error) {
  console.error("Error initializing Stripe:", error);
  // Initialize without a key for now, will fail gracefully when called
  stripe = require('stripe')();
}

/**
 * Creates a Stripe Crypto Onramp session for purchasing USDC
 *
 * @param {string} usdcAmount - The amount of USDC to purchase
 * @param {string} userId - User ID for MVT wallet transfer
 * @param {string} mvtAmount - The amount of MVT tokens to transfer after purchase
 * @param {string} exchangeRate - The exchange rate used for conversion
 * @returns {Promise<{id: string, client_secret: string}>} - Onramp session details
 */
function createOnrampSession(usdcAmount, userId, mvtAmount, exchangeRate) {
  return new Promise(function(resolve, reject) {
    try {
      console.log("Creating Stripe Crypto Onramp session for " + usdcAmount + " USDC for user " + userId);
      console.log("MVT Amount: " + mvtAmount + ", Exchange Rate: " + exchangeRate);

      if (!process.env.STRIPE_SECRET_KEY) {
        throw new Error('STRIPE_SECRET_KEY environment variable is not set. Please configure your API key.');
      }

      if (!usdcAmount || isNaN(parseFloat(usdcAmount)) || parseFloat(usdcAmount) <= 0) {
        throw new Error('Invalid USDC amount provided');
      }

      if (!userId || userId.length < 3) {
        throw new Error('Invalid user ID provided');
      }

      if (!mvtAmount || isNaN(parseFloat(mvtAmount)) || parseFloat(mvtAmount) <= 0) {
        throw new Error('Invalid MVT amount provided');
      }

      if (!exchangeRate || isNaN(parseFloat(exchangeRate)) || parseFloat(exchangeRate) <= 0) {
        throw new Error('Invalid exchange rate provided');
      }

      const withdrawContractAddress = process.env.MVT_WITHDRAW_CONTRACT_ADDRESS;
      
      if (!withdrawContractAddress) {
        throw new Error('MVT_USDC_CONTRACT_ADDRESS environment variable is not set');
      }

      // Determine if we're in test mode
      let isTestMode = process.env.NODE_ENV !== 'production' || process.env.STRIPE_SECRET_KEY.startsWith('sk_test_');
      console.log("Operating in " + (isTestMode ? "TEST" : "PRODUCTION") + " mode");

      let sessionParams = {
        'destination_currencies[]': 'usdc',
        'destination_currency': 'usdc',
        'destination_networks[]': 'ethereum',
        'destination_network': 'ethereum',
        'wallet_addresses[ethereum]': withdrawContractAddress,
        'lock_wallet_address': true,
        'source_currency': 'usd',
        'source_amount': parseFloat(usdcAmount),
        'metadata[user_id]': userId,
        'metadata[transaction_type]': 'usdc_to_mvt',
        'metadata[mvt_amount]': mvtAmount,
        'metadata[exchange_rate]': exchangeRate,
        'metadata[usdc_amount]': usdcAmount,
      };
      
      console.log('sessionParams: ', sessionParams);

      if (isTestMode) {
        console.log("Adding test parameters to simplify KYC process");
      }
      
      axios.post(
        'https://api.stripe.com/v1/crypto/onramp_sessions',
        new URLSearchParams(sessionParams),
        {
          headers: {
            'Authorization': 'Bearer ' + process.env.STRIPE_SECRET_KEY,
            'Content-Type': 'application/x-www-form-urlencoded'
          }
        }
      ).then(function(response) {
        const session = response.data;
        console.log("Stripe Onramp session created successfully:", session.id);
        console.log("Session livemode:", session.livemode ? "LIVE" : "TEST");
        
        if (session.redirect_url) {
          console.log("Redirect URL for testing:", session.redirect_url);
        }
        
        if (!session.livemode || isTestMode) {
          console.log("TEST MODE ACTIVE: Use these test credentials for KYC:");
          console.log("- Email: any test email");
          console.log("- Phone: +18004444444");
          console.log("- OTP Code: 000000");
          console.log("- Name: John Verified");
          console.log("- Birthday: January 1, 1901");
          console.log("- SSN: 000000000");
          console.log("- Address: address_full_match, Seattle, WA 12345");
          console.log("- Test Card: ****************, exp: 12/24, cvc: 123");
        }
        
        resolve({
          id: session.id,
          client_secret: session.client_secret,
          livemode: session.livemode
        });
      }).catch(function(error) {
        console.error('Error creating Stripe Onramp session:', error.response ? error.response.data : error.message);
        reject(new Error("Failed to create onramp session: " + (error.response?.data?.error?.message || error.message)));
      });
    } catch (error) {
      console.error('Error creating Stripe Crypto Onramp session:', error);
      reject(new Error("Failed to create onramp session: " + error.message));
    }
  });
}

// Webhook functionality removed - using frontend-triggered transfers instead

module.exports = {
  createOnrampSession
};
