/**
 * Transaction Service Test Suite
 * Tests for transaction business logic, database operations, and MVT token management
 */

const transactionService = require('../transaction.service');
const walletService = require('../../wallet/wallet.service');
const { mockDynamoDB } = require('../../../__tests__/setup');

// Mock dependencies
jest.mock('../../../config/aws', () => ({
  AWS: {
    DynamoDB: {
      Converter: {
        marshall: jest.fn((item) => item),
        unmarshall: jest.fn((item) => item)
      }
    }
  },
  ddb: mockDynamoDB
}));

jest.mock('../../../shared/database/dynamoUtils', () => ({
  getTableName: jest.fn((tableName) => `test-${tableName}`),
  tableExists: jest.fn().mockResolvedValue(true)
}));

jest.mock('../../wallet/wallet.service');

describe('Transaction Service', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('generateTransactionId', () => {
    test('should generate unique transaction ID with prefix', () => {
      // Act
      const id1 = transactionService.generateTransactionId('mint');
      const id2 = transactionService.generateTransactionId('mint');

      // Assert
      expect(id1).toMatch(/^mint-\d+-[a-z0-9]+$/);
      expect(id2).toMatch(/^mint-\d+-[a-z0-9]+$/);
      expect(id1).not.toBe(id2);
    });

    test('should handle different prefixes', () => {
      // Act
      const mintId = transactionService.generateTransactionId('mint');
      const transferId = transactionService.generateTransactionId('transfer');
      const swapId = transactionService.generateTransactionId('swap');

      // Assert
      expect(mintId).toMatch(/^mint-/);
      expect(transferId).toMatch(/^transfer-/);
      expect(swapId).toMatch(/^swap-/);
    });

    test('should generate IDs with timestamp component', () => {
      // Arrange
      const beforeTime = Date.now();

      // Act
      const id = transactionService.generateTransactionId('test');

      // Assert
      const afterTime = Date.now();
      const timestampMatch = id.match(/test-(\d+)-/);
      expect(timestampMatch).toBeTruthy();

      const timestamp = parseInt(timestampMatch[1]);
      expect(timestamp).toBeGreaterThanOrEqual(beforeTime);
      expect(timestamp).toBeLessThanOrEqual(afterTime);
    });
  });

  describe('validateTransactionType', () => {
    test('should return valid transaction types unchanged', () => {
      // Arrange
      const validTypes = [
        'ADMIN_MINT',
        'CENTRAL_TO_USER_TRANSFER',
        'USER_TO_USER_TRANSFER',
        'USDC_DEPOSIT',
        'SWAP_REQUEST',
        'SWAP_APPROVED'
      ];

      // Act & Assert
      validTypes.forEach(type => {
        expect(transactionService.validateTransactionType(type)).toBe(type);
      });
    });

    test('should return default for invalid transaction types', () => {
      // Arrange
      const invalidTypes = [
        'INVALID_TYPE',
        'OLD_MINT_TYPE',
        'DEPRECATED_TRANSFER',
        ''
      ];

      // Act & Assert
      invalidTypes.forEach(type => {
        expect(transactionService.validateTransactionType(type)).toBe('SYSTEM_ADJUSTMENT');
      });
    });

    test('should handle null and undefined values', () => {
      // Act & Assert
      expect(transactionService.validateTransactionType(null)).toBe('SYSTEM_ADJUSTMENT');
      expect(transactionService.validateTransactionType(undefined)).toBe('SYSTEM_ADJUSTMENT');
    });

    test('should handle non-string values', () => {
      // Act & Assert
      expect(transactionService.validateTransactionType(123)).toBe('SYSTEM_ADJUSTMENT');
      expect(transactionService.validateTransactionType({})).toBe('SYSTEM_ADJUSTMENT');
      expect(transactionService.validateTransactionType([])).toBe('SYSTEM_ADJUSTMENT');
    });
  });

  describe('mapTransactionDisplayType', () => {
    test('should map ADMIN_MINT to ADDED', () => {
      // Act
      const result = transactionService.mapTransactionDisplayType('ADMIN_MINT', {}, null, false);

      // Assert
      expect(result).toBe('ADDED');
    });

    test('should map CENTRAL_TO_USER_TRANSFER based on context', () => {
      // Arrange
      const transaction = { fromUserId: 'central-wallet', toUserId: 'user123' };

      // Act - Admin view all
      const adminResult = transactionService.mapTransactionDisplayType('CENTRAL_TO_USER_TRANSFER', transaction, null, true);

      // Act - User view
      const userResult = transactionService.mapTransactionDisplayType('CENTRAL_TO_USER_TRANSFER', transaction, 'user123', false);

      // Assert
      expect(adminResult).toBe('SENT');
      expect(userResult).toBe('RECEIVED');
    });

    test('should map USER_TO_USER_TRANSFER based on viewing user', () => {
      // Arrange
      const transaction = { fromUserId: 'user123', toUserId: 'user456' };

      // Act
      const senderView = transactionService.mapTransactionDisplayType('USER_TO_USER_TRANSFER', transaction, 'user123', false);
      const receiverView = transactionService.mapTransactionDisplayType('USER_TO_USER_TRANSFER', transaction, 'user456', false);

      // Assert
      expect(senderView).toBe('SENT');
      expect(receiverView).toBe('RECEIVED');
    });

    test('should map USDC operations correctly', () => {
      // Act & Assert
      expect(transactionService.mapTransactionDisplayType('USDC_DEPOSIT', {}, null, false)).toBe('ADDED');
      expect(transactionService.mapTransactionDisplayType('USDC_WITHDRAWAL', {}, null, false)).toBe('SENT');
    });
  });

  describe('generateTransactionDisplayDetails', () => {
    test('should generate correct details for SENT transaction', () => {
      // Arrange
      const transaction = {
        toUser: { givenName: 'John', familyName: 'Doe' },
        tokenType: 'MVT'
      };

      // Act
      const result = transactionService.generateTransactionDisplayDetails(transaction, 'SENT');

      // Assert
      expect(result.displayType).toBe('SENT');
      expect(result.primaryLabel).toBe('To: John Doe');
      expect(result.showEtherscanLink).toBe(false);
    });

    test('should generate correct details for RECEIVED transaction', () => {
      // Arrange
      const transaction = {
        fromUser: { givenName: 'Jane', familyName: 'Smith' },
        tokenType: 'USDC'
      };

      // Act
      const result = transactionService.generateTransactionDisplayDetails(transaction, 'RECEIVED');

      // Assert
      expect(result.displayType).toBe('RECEIVED');
      expect(result.primaryLabel).toBe('From: Jane Smith');
      expect(result.showEtherscanLink).toBe(true);
    });

    test('should handle admin minting transactions', () => {
      // Arrange
      const transaction = {
        transactionType: 'ADMIN_MINT',
        tokenType: 'MVT'
      };

      // Act
      const result = transactionService.generateTransactionDisplayDetails(transaction, 'ADDED');

      // Assert
      expect(result.displayType).toBe('ADDED');
      expect(result.primaryLabel).toBe('From: Admin');
      expect(result.showEtherscanLink).toBe(false);
    });

    test('should handle swap approved MVT transaction', () => {
      // Arrange
      const transaction = {
        transactionType: 'SWAP_APPROVED',
        tokenType: 'MVT',
        toUser: { givenName: 'Central', familyName: 'Wallet' }
      };

      // Act
      const result = transactionService.generateTransactionDisplayDetails(transaction, 'SENT');

      // Assert
      expect(result.displayType).toBe('SENT');
      expect(result.secondaryInfo).toBe('Swap: MVT Exchanged');
      expect(result.showEtherscanLink).toBe(false);
    });

    test('should handle swap approved USDC transaction', () => {
      // Arrange
      const transaction = {
        transactionType: 'SWAP_APPROVED',
        tokenType: 'USDC',
        fromUser: { givenName: 'Liquidity', familyName: 'Pool' }
      };

      // Act
      const result = transactionService.generateTransactionDisplayDetails(transaction, 'RECEIVED');

      // Assert
      expect(result.displayType).toBe('RECEIVED');
      expect(result.secondaryInfo).toBe('Swap: USDC Received');
      expect(result.showEtherscanLink).toBe(true);
    });

    test('should handle swap rejected transaction', () => {
      // Arrange
      const transaction = {
        transactionType: 'SWAP_REJECTED',
        tokenType: 'MVT'
      };

      // Act
      const result = transactionService.generateTransactionDisplayDetails(transaction, 'SENT');

      // Assert
      expect(result.displayType).toBe('SENT');
      expect(result.secondaryInfo).toBe('Swap: Request Rejected');
      expect(result.showEtherscanLink).toBe(false);
    });
  });

  describe('createMVTWalletTransaction', () => {
    test('should create transaction record successfully', async () => {
      // Arrange
      const transactionData = {
        id: 'test-tx-123',
        transactionType: 'ADMIN_MINT',
        amount: 1000,
        fromWalletId: 'central-mvt-wallet',
        toWalletId: 'user-wallet-test-user',
        status: 'COMPLETED',
        description: 'Test mint transaction'
      };
      
      mockDynamoDB.putItem.mockReturnValue(global.testUtils.mockDynamoDBSuccess());

      // Act
      const result = await transactionService.createMVTWalletTransaction(transactionData);

      // Assert
      expect(mockDynamoDB.putItem).toHaveBeenCalledWith(
        expect.objectContaining({
          TableName: 'test-MVTWalletTransaction',
          Item: expect.objectContaining({
            id: 'test-tx-123',
            transactionType: 'ADMIN_MINT',
            amount: 1000,
            status: 'COMPLETED'
          })
        })
      );
      expect(result).toBe(true);
    });

    test('should handle DynamoDB errors gracefully', async () => {
      // Arrange
      const transactionData = {
        id: 'test-tx-123',
        transactionType: 'ADMIN_MINT',
        amount: 1000
      };
      
      mockDynamoDB.putItem.mockReturnValue(
        global.testUtils.mockDynamoDBError(new Error('DynamoDB error'))
      );

      // Act
      const result = await transactionService.createMVTWalletTransaction(transactionData);

      // Assert
      expect(result).toBe(false);
    });

    test('should set default values for missing fields', async () => {
      // Arrange
      const minimalTransactionData = {
        id: 'test-tx-123',
        transactionType: 'ADMIN_MINT',
        amount: 1000
      };

      mockDynamoDB.putItem.mockReturnValue(global.testUtils.mockDynamoDBSuccess());

      // Act
      await transactionService.createMVTWalletTransaction(minimalTransactionData);

      // Assert
      expect(mockDynamoDB.putItem).toHaveBeenCalledWith(
        expect.objectContaining({
          Item: expect.objectContaining({
            isDeleted: 'false',
            __typename: 'MVTWalletTransaction'
          })
        })
      );
    });

    test('should sanitize invalid transaction types', async () => {
      // Arrange
      const transactionDataWithInvalidType = {
        id: 'test-tx-123',
        transactionType: 'INVALID_TYPE',
        amount: 1000
      };

      mockDynamoDB.putItem.mockReturnValue(global.testUtils.mockDynamoDBSuccess());

      // Act
      await transactionService.createMVTWalletTransaction(transactionDataWithInvalidType);

      // Assert
      expect(mockDynamoDB.putItem).toHaveBeenCalledWith(
        expect.objectContaining({
          Item: expect.objectContaining({
            transactionType: 'SYSTEM_ADJUSTMENT' // Should be sanitized to default
          })
        })
      );
    });

    test('should use explicit tokenType when provided', async () => {
      // Arrange
      const transactionDataWithTokenType = {
        id: 'test-tx-123',
        transactionType: 'SWAP_APPROVED',
        tokenType: 'USDC',
        amount: 100.50
      };

      mockDynamoDB.putItem.mockReturnValue(global.testUtils.mockDynamoDBSuccess());

      // Act
      await transactionService.createMVTWalletTransaction(transactionDataWithTokenType);

      // Assert
      expect(mockDynamoDB.putItem).toHaveBeenCalledWith(
        expect.objectContaining({
          Item: expect.objectContaining({
            tokenType: 'USDC' // Should use explicit tokenType
          })
        })
      );
    });

    test('should default to MVT tokenType when not provided', async () => {
      // Arrange
      const transactionDataWithoutTokenType = {
        id: 'test-tx-123',
        transactionType: 'ADMIN_MINT',
        amount: 1000
      };

      mockDynamoDB.putItem.mockReturnValue(global.testUtils.mockDynamoDBSuccess());

      // Act
      await transactionService.createMVTWalletTransaction(transactionDataWithoutTokenType);

      // Assert
      expect(mockDynamoDB.putItem).toHaveBeenCalledWith(
        expect.objectContaining({
          Item: expect.objectContaining({
            tokenType: 'MVT' // Should default to MVT
          })
        })
      );
    });
  });

  describe('mintMVTTokens', () => {
    test('should mint tokens successfully', async () => {
      // Arrange
      const amount = 1000;
      const description = 'Test mint';
      const adminUserId = 'admin-123';
      
      walletService.updateCentralWalletBalance.mockResolvedValue(true);
      mockDynamoDB.putItem.mockReturnValue(global.testUtils.mockDynamoDBSuccess());

      // Act
      const result = await transactionService.mintMVTTokens(amount, description, adminUserId);

      // Assert
      expect(walletService.updateCentralWalletBalance).toHaveBeenCalledWith(amount, 'ADD');
      expect(result).toMatchObject({
        transactionType: 'ADMIN_MINT',
        amount: 1000,
        status: 'COMPLETED',
        adminUserId: 'admin-123'
      });
    });

    test('should validate mint parameters', async () => {
      // Act & Assert
      await expect(transactionService.mintMVTTokens(0, 'test', 'admin')).rejects.toThrow('Amount must be greater than 0');
      await expect(transactionService.mintMVTTokens(-100, 'test', 'admin')).rejects.toThrow('Amount must be greater than 0');
      await expect(transactionService.mintMVTTokens(1.5, 'test', 'admin')).rejects.toThrow('Amount must be an integer');
      await expect(transactionService.mintMVTTokens(100, '', 'admin')).rejects.toThrow('Description is required');
      await expect(transactionService.mintMVTTokens(100, 'test', '')).rejects.toThrow('Admin user ID is required');
    });

    test('should handle wallet update failures', async () => {
      // Arrange
      walletService.updateCentralWalletBalance.mockRejectedValue(new Error('Wallet update failed'));

      // Act & Assert
      await expect(
        transactionService.mintMVTTokens(1000, 'test', 'admin-123')
      ).rejects.toThrow('Failed to mint MVT tokens');
    });

    test('should generate unique transaction IDs for concurrent mints', async () => {
      // Arrange
      walletService.updateCentralWalletBalance.mockResolvedValue(true);
      mockDynamoDB.putItem.mockReturnValue(global.testUtils.mockDynamoDBSuccess());

      // Act
      const [result1, result2] = await Promise.all([
        transactionService.mintMVTTokens(1000, 'Mint 1', 'admin-123'),
        transactionService.mintMVTTokens(2000, 'Mint 2', 'admin-123')
      ]);

      // Assert
      expect(result1.id).not.toBe(result2.id);
      expect(result1.id).toMatch(/^mint-/);
      expect(result2.id).toMatch(/^mint-/);
    });
  });

  describe('transferMVTToUser', () => {
    test('should transfer tokens from central to user successfully', async () => {
      // Arrange
      const userId = 'test-user-123';
      const amount = 500;
      const description = 'Admin transfer';
      const adminUserId = 'admin-123';
      
      walletService.updateCentralWalletBalance.mockResolvedValue(true);
      walletService.updateUserBalance.mockResolvedValue(true);
      mockDynamoDB.putItem.mockReturnValue(global.testUtils.mockDynamoDBSuccess());

      // Act
      const result = await transactionService.transferMVTToUser(userId, amount, description, adminUserId);

      // Assert
      expect(walletService.updateCentralWalletBalance).toHaveBeenCalledWith(amount, 'SUBTRACT');
      expect(walletService.updateUserBalance).toHaveBeenCalledWith(userId, amount, 'ADD');
      expect(result).toMatchObject({
        transactionType: 'CENTRAL_TO_USER_TRANSFER',
        amount: 500,
        toUserId: userId,
        adminUserId: adminUserId
      });
    });

    test('should validate transfer parameters', async () => {
      // Act & Assert
      await expect(transactionService.transferMVTToUser('', 100, 'test', 'admin')).rejects.toThrow('User ID is required');
      await expect(transactionService.transferMVTToUser('user', 0, 'test', 'admin')).rejects.toThrow('Amount must be greater than 0');
      await expect(transactionService.transferMVTToUser('user', 100, '', 'admin')).rejects.toThrow('Description is required');
      await expect(transactionService.transferMVTToUser('user', 100, 'test', '')).rejects.toThrow('Admin user ID is required');
    });

    test('should handle insufficient central wallet balance', async () => {
      // Arrange
      walletService.updateCentralWalletBalance.mockRejectedValue(new Error('Insufficient balance'));

      // Act & Assert
      await expect(
        transactionService.transferMVTToUser('user-123', 1000, 'test', 'admin-123')
      ).rejects.toThrow('Failed to transfer MVT tokens');
    });

    test('should rollback central wallet on user wallet failure', async () => {
      // Arrange
      walletService.updateCentralWalletBalance.mockResolvedValue(true);
      walletService.updateUserBalance.mockRejectedValue(new Error('User wallet error'));

      // Act & Assert
      await expect(
        transactionService.transferMVTToUser('user-123', 500, 'test', 'admin-123')
      ).rejects.toThrow('Failed to transfer MVT tokens');
    });
  });

  describe('transferMVTBetweenUsers', () => {
    test('should transfer tokens between users successfully', async () => {
      // Arrange
      const fromUserId = 'sender-123';
      const toUserId = 'recipient-123';
      const amount = 200;
      const description = 'User transfer';
      
      walletService.updateUserBalance
        .mockResolvedValueOnce(true) // Subtract from sender
        .mockResolvedValueOnce(true); // Add to recipient
      mockDynamoDB.putItem.mockReturnValue(global.testUtils.mockDynamoDBSuccess());

      // Act
      const result = await transactionService.transferMVTBetweenUsers(fromUserId, toUserId, amount, description);

      // Assert
      expect(walletService.updateUserBalance).toHaveBeenCalledWith(fromUserId, amount, 'SUBTRACT');
      expect(walletService.updateUserBalance).toHaveBeenCalledWith(toUserId, amount, 'ADD');
      expect(result).toMatchObject({
        transactionType: 'USER_TO_USER_TRANSFER',
        amount: 200,
        fromUserId: fromUserId,
        toUserId: toUserId
      });
    });

    test('should prevent self-transfer', async () => {
      // Act & Assert
      await expect(
        transactionService.transferMVTBetweenUsers('user-123', 'user-123', 100, 'self transfer')
      ).rejects.toThrow('Cannot transfer to yourself');
    });

    test('should validate user transfer parameters', async () => {
      // Act & Assert
      await expect(transactionService.transferMVTBetweenUsers('', 'to', 100, 'test')).rejects.toThrow('From user ID is required');
      await expect(transactionService.transferMVTBetweenUsers('from', '', 100, 'test')).rejects.toThrow('To user ID is required');
      await expect(transactionService.transferMVTBetweenUsers('from', 'to', 0, 'test')).rejects.toThrow('Amount must be greater than 0');
    });

    test('should handle insufficient sender balance', async () => {
      // Arrange
      walletService.updateUserBalance.mockRejectedValue(new Error('Insufficient balance'));

      // Act & Assert
      await expect(
        transactionService.transferMVTBetweenUsers('sender-123', 'recipient-123', 1000, 'test')
      ).rejects.toThrow('Failed to transfer MVT tokens between users');
    });

    test('should rollback sender balance on recipient failure', async () => {
      // Arrange
      walletService.updateUserBalance
        .mockResolvedValueOnce(true) // Subtract from sender succeeds
        .mockRejectedValueOnce(new Error('Recipient wallet error')); // Add to recipient fails

      // Act & Assert
      await expect(
        transactionService.transferMVTBetweenUsers('sender-123', 'recipient-123', 200, 'test')
      ).rejects.toThrow('Failed to transfer MVT tokens between users');
    });
  });

  describe('getMVTWalletTransactionList', () => {
    test('should retrieve user transactions successfully', async () => {
      // Arrange
      const userId = 'test-user-123';
      const mockTransactions = {
        Items: [
          global.testUtils.createMockTransaction('tx-1', 'ADMIN_MINT', 100),
          global.testUtils.createMockTransaction('tx-2', 'USER_TO_USER_TRANSFER', 50)
        ]
      };
      
      mockDynamoDB.scan.mockReturnValue(global.testUtils.mockDynamoDBSuccess(mockTransactions));

      // Act
      const result = await transactionService.getMVTWalletTransactionList(userId, false, 10);

      // Assert
      expect(mockDynamoDB.scan).toHaveBeenCalledWith(
        expect.objectContaining({
          TableName: 'test-MVTWalletTransaction',
          FilterExpression: expect.stringContaining('fromUserId = :userId OR toUserId = :userId'),
          ExpressionAttributeValues: expect.objectContaining({
            ':userId': userId,
            ':isDeleted': 'false'
          }),
          Limit: 10
        })
      );
      expect(result).toHaveLength(2);
    });

    test('should retrieve all transactions for admin', async () => {
      // Arrange
      const mockTransactions = {
        Items: [
          global.testUtils.createMockTransaction('tx-1', 'ADMIN_MINT', 100),
          global.testUtils.createMockTransaction('tx-2', 'CENTRAL_TO_USER_TRANSFER', 200)
        ]
      };
      
      mockDynamoDB.scan.mockReturnValue(global.testUtils.mockDynamoDBSuccess(mockTransactions));

      // Act
      const result = await transactionService.getMVTWalletTransactionList(null, true, 20);

      // Assert
      expect(mockDynamoDB.scan).toHaveBeenCalledWith(
        expect.objectContaining({
          FilterExpression: 'isDeleted = :isDeleted',
          ExpressionAttributeValues: { ':isDeleted': 'false' },
          Limit: 20
        })
      );
      expect(result).toHaveLength(2);
    });

    test('should handle empty transaction list', async () => {
      // Arrange
      mockDynamoDB.scan.mockReturnValue(global.testUtils.mockDynamoDBSuccess({ Items: [] }));

      // Act
      const result = await transactionService.getMVTWalletTransactionList('user-123', false, 10);

      // Assert
      expect(result).toEqual([]);
    });

    test('should apply default limit when not specified', async () => {
      // Arrange
      mockDynamoDB.scan.mockReturnValue(global.testUtils.mockDynamoDBSuccess({ Items: [] }));

      // Act
      await transactionService.getMVTWalletTransactionList('user-123', false);

      // Assert
      expect(mockDynamoDB.scan).toHaveBeenCalledWith(
        expect.objectContaining({
          Limit: 50 // Default limit
        })
      );
    });
  });

  describe('getMVTWalletTransactionById', () => {
    test('should retrieve transaction by ID successfully', async () => {
      // Arrange
      const transactionId = 'test-tx-123';
      const mockTransaction = {
        Item: global.testUtils.createMockTransaction(transactionId, 'ADMIN_MINT', 100)
      };
      
      mockDynamoDB.getItem.mockReturnValue(global.testUtils.mockDynamoDBSuccess(mockTransaction));

      // Act
      const result = await transactionService.getMVTWalletTransactionById(transactionId);

      // Assert
      expect(mockDynamoDB.getItem).toHaveBeenCalledWith(
        expect.objectContaining({
          TableName: 'test-MVTWalletTransaction',
          Key: { id: transactionId }
        })
      );
      expect(result.id).toBe(transactionId);
    });

    test('should return null for non-existent transaction', async () => {
      // Arrange
      mockDynamoDB.getItem.mockReturnValue(global.testUtils.mockDynamoDBSuccess({}));

      // Act
      const result = await transactionService.getMVTWalletTransactionById('non-existent');

      // Assert
      expect(result).toBeNull();
    });

    test('should validate transaction ID parameter', async () => {
      // Act & Assert
      await expect(transactionService.getMVTWalletTransactionById('')).rejects.toThrow('Transaction ID is required');
      await expect(transactionService.getMVTWalletTransactionById(null)).rejects.toThrow('Transaction ID is required');
    });
  });

  describe('Race Condition Prevention', () => {
    test('should handle concurrent transfers safely', async () => {
      // Arrange
      walletService.updateUserBalance.mockResolvedValue(true);
      mockDynamoDB.putItem.mockReturnValue(global.testUtils.mockDynamoDBSuccess());

      // Act
      const transfers = await Promise.all([
        transactionService.transferMVTBetweenUsers('user1', 'user2', 100, 'Transfer 1'),
        transactionService.transferMVTBetweenUsers('user3', 'user4', 200, 'Transfer 2')
      ]);

      // Assert
      expect(transfers).toHaveLength(2);
      expect(transfers[0].id).not.toBe(transfers[1].id);
      expect(walletService.updateUserBalance).toHaveBeenCalledTimes(4); // 2 transfers × 2 operations each
    });
  });

  describe('getTransactionsByMetadata', () => {
    test('should find transactions by metadata criteria', async () => {
      // Arrange
      const mockTransactions = {
        Items: [
          {
            id: { S: 'tx-1' },
            transactionType: { S: 'CENTRAL_TO_USER_TRANSFER' },
            amount: { N: '100' },
            metadata: { S: '{"sessionId":"session-123","transferType":"onramp_purchase"}' },
            isDeleted: { S: 'false' }
          },
          {
            id: { S: 'tx-2' },
            transactionType: { S: 'ADMIN_MINT' },
            amount: { N: '200' },
            metadata: { S: '{"operation":"mint","adminId":"admin-123"}' },
            isDeleted: { S: 'false' }
          }
        ]
      };

      mockDynamoDB.scan.mockReturnValue(global.testUtils.mockDynamoDBSuccess(mockTransactions));

      // Act
      const result = await transactionService.getTransactionsByMetadata({
        sessionId: 'session-123',
        transferType: 'onramp_purchase'
      });

      // Assert
      expect(mockDynamoDB.scan).toHaveBeenCalledWith(
        expect.objectContaining({
          FilterExpression: expect.stringContaining('isDeleted = :isDeleted'),
          ExpressionAttributeValues: expect.objectContaining({
            ':isDeleted': { S: 'false' }
          })
        })
      );
      expect(result).toHaveLength(1);
      expect(result[0].id).toBe('tx-1');
    });

    test('should return empty array when no matches found', async () => {
      // Arrange
      mockDynamoDB.scan.mockReturnValue(global.testUtils.mockDynamoDBSuccess({ Items: [] }));

      // Act
      const result = await transactionService.getTransactionsByMetadata({
        sessionId: 'nonexistent-session'
      });

      // Assert
      expect(result).toEqual([]);
    });

    test('should handle table not found error', async () => {
      // Arrange
      const { tableExists } = require('../../../shared/database/dynamoUtils');
      tableExists.mockResolvedValueOnce(false);

      // Act
      const result = await transactionService.getTransactionsByMetadata({
        sessionId: 'session-123'
      });

      // Assert
      expect(result).toEqual([]);
    });

    test('should handle DynamoDB scan errors', async () => {
      // Arrange
      mockDynamoDB.scan.mockReturnValue(
        global.testUtils.mockDynamoDBError(new Error('Scan failed'))
      );

      // Act & Assert
      await expect(
        transactionService.getTransactionsByMetadata({ sessionId: 'session-123' })
      ).rejects.toThrow('Scan failed');
    });
  });
});
