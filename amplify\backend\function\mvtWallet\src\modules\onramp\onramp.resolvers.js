const transactionService = require('../transaction/transaction.service');
const responseUtils = require('../../shared/utils/responseUtils');
const { createLogger, logError, logSuccess } = require('../../shared/utils/logger');
const standardizedErrorUtils = require('../../shared/utils/standardizedErrorUtils');

/**
 * Process frontend-triggered onramp transfer
 * @param {object} event - GraphQL event
 * @param {object} args - GraphQL arguments
 * @returns {Promise<object>} - Response object
 */
async function processOnrampTransfer(event, args) {
  const logger = createLogger({}, 'processOnrampTransfer', 'OnrampTransfer', args);
  
  try {
    const { userId, mvtAmount, usdcAmount, exchangeRate, sessionId, description } = args;
    
    // Validate required parameters
    if (!userId || !mvtAmount || !usdcAmount || !exchangeRate || !sessionId) {
      return standardizedErrorUtils.createValidationError(
        'processOnrampTransfer',
        'Missing required parameters: userId, mvtAmount, usdcAmount, exchangeRate, and sessionId are all required',
        'input'
      );
    }

    if (mvtAmount <= 0 || usdcAmount <= 0 || exchangeRate <= 0) {
      return standardizedErrorUtils.createValidationError(
        'processOnrampTransfer',
        'Invalid amounts provided: mvtAmount, usdcAmount, and exchangeRate must be greater than 0',
        'amounts'
      );
    }

    logger.info({ 
      userId, 
      mvtAmount, 
      usdcAmount, 
      exchangeRate, 
      sessionId 
    }, `Processing onramp transfer for user ${userId}`);

    // Check if transfer was already processed for this session
    const existingTransfer = await checkExistingTransfer(sessionId);
    if (existingTransfer) {
      logger.info({ sessionId }, 'Transfer already processed for this session');
      return responseUtils.createSuccessResponse(
        {
          mvtTransactionId: existingTransfer.id,
          usdcTransactionId: existingTransfer.relatedTransactionId,
          newBalance: existingTransfer.newBalance,
          transferAmount: existingTransfer.amount,
          alreadyProcessed: true
        },
        "Transfer already completed for this session"
      );
    }

    // Process the MVT transfer
    const transferDescription = description || `Frontend onramp transfer: ${usdcAmount} USDC → ${mvtAmount} MVT (rate: ${exchangeRate})`;
    
    const transferResult = await transactionService.transferMVTToUser(
      userId,
      mvtAmount,
      transferDescription,
      'onramp-system' // System user for onramp transfers
    );

    // transferMVTToUser returns transaction data directly if successful, throws error if failed
    if (!transferResult?.id || transferResult?.status !== 'COMPLETED') {
      throw new Error('MVT transfer failed - transaction not completed');
    }

    // Create USDC transaction record for audit trail
    const usdcTransactionResult = await createUSDCTransactionRecord(
      userId,
      usdcAmount,
      sessionId,
      transferResult.id // Use the correct transaction ID from transferResult
    );

    logSuccess(logger, 'processOnrampTransfer', transferResult, {
      userId,
      mvtAmount,
      sessionId,
      mvtTransactionId: transferResult.id,
      usdcTransactionId: usdcTransactionResult?.transactionId
    });

    return responseUtils.createSuccessResponse(
      {
        mvtTransactionId: transferResult.id,
        usdcTransactionId: usdcTransactionResult?.transactionId,
        newBalance: transferResult.metadata ? JSON.parse(transferResult.metadata).newUserBalance : null,
        transferAmount: mvtAmount
      },
      "Onramp transfer processed successfully"
    );

  } catch (error) {
    logError(logger, error, 'processOnrampTransfer', args);
    return standardizedErrorUtils.createAutoDetectedError(
      'processOnrampTransfer',
      error,
      'onramp transfer processing'
    );
  }
}

/**
 * Check onramp transfer status for a session
 * @param {object} event - GraphQL event
 * @param {object} args - GraphQL arguments
 * @returns {Promise<object>} - Response object
 */
async function checkOnrampTransferStatus(event, args) {
  const logger = createLogger({}, 'checkOnrampTransferStatus', 'OnrampStatus', args);
  
  try {
    const { sessionId } = args;
    
    if (!sessionId) {
      return standardizedErrorUtils.createValidationError(
        'checkOnrampTransferStatus',
        'Session ID is required',
        'sessionId'
      );
    }

    logger.info({ sessionId }, `Checking transfer status for session ${sessionId}`);

    const existingTransfer = await checkExistingTransfer(sessionId);
    
    const response = {
      processed: !!existingTransfer,
      transactionId: existingTransfer?.id || null,
      timestamp: existingTransfer?.createdAt || null
    };

    logSuccess(logger, 'checkOnrampTransferStatus', response, { sessionId });

    return responseUtils.createSuccessResponse(
      response,
      existingTransfer ? "Transfer found" : "No transfer found for this session"
    );

  } catch (error) {
    logError(logger, error, 'checkOnrampTransferStatus', args);
    return standardizedErrorUtils.createAutoDetectedError(
      'checkOnrampTransferStatus',
      error,
      'transfer status check'
    );
  }
}

/**
 * Verify onramp session (placeholder for future Stripe API integration)
 * @param {object} event - GraphQL event
 * @param {object} args - GraphQL arguments
 * @returns {Promise<object>} - Response object
 */
async function verifyOnrampSession(event, args) {
  const logger = createLogger({}, 'verifyOnrampSession', 'OnrampVerify', args);
  
  try {
    const { sessionId } = args;
    
    if (!sessionId) {
      return standardizedErrorUtils.createValidationError(
        'verifyOnrampSession',
        'Session ID is required',
        'sessionId'
      );
    }

    logger.info({ sessionId }, `Verifying onramp session ${sessionId}`);

    // For now, return success - in future this could call Stripe API to verify session
    const response = {
      sessionId: sessionId,
      status: 'completed',
      completed: true,
      transactionDetails: {
        verified: true,
        timestamp: new Date().toISOString()
      }
    };

    logSuccess(logger, 'verifyOnrampSession', response, { sessionId });

    return responseUtils.createSuccessResponse(
      response,
      "Session verification completed"
    );

  } catch (error) {
    logError(logger, error, 'verifyOnrampSession', args);
    return standardizedErrorUtils.createAutoDetectedError(
      'verifyOnrampSession',
      error,
      'session verification'
    );
  }
}

/**
 * Check if transfer already exists for this session
 * @param {string} sessionId - Session ID to check
 * @returns {Promise<object|null>} - Existing transfer or null
 */
async function checkExistingTransfer(sessionId) {
  try {
    // Query MVTWalletTransaction table for existing transfer with this sessionId
    const existingTransactions = await transactionService.getTransactionsByMetadata({
      sessionId: sessionId,
      transferType: 'onramp_purchase'
    });

    return existingTransactions && existingTransactions.length > 0 ? existingTransactions[0] : null;
  } catch (error) {
    console.error('Error checking existing transfer:', error);
    return null;
  }
}

/**
 * Create USDC transaction record for audit trail
 * @param {string} userId - User ID
 * @param {number} usdcAmount - USDC amount
 * @param {string} sessionId - Session ID
 * @param {string} mvtTransactionId - Related MVT transaction ID
 * @returns {Promise<object>} - Transaction result
 */
async function createUSDCTransactionRecord(userId, usdcAmount, sessionId, mvtTransactionId) {
  try {
    const transactionId = transactionService.generateTransactionId('usdc-purchase');
    const now = new Date().toISOString();

    // Create a record of the USDC purchase for audit purposes using the MVT wallet transaction system
    const usdcTransactionData = {
      id: transactionId,
      transactionType: 'USDC_DEPOSIT', // Use existing USDC transaction type
      tokenType: 'USDC',
      amount: usdcAmount,
      fromWalletId: 'stripe-onramp',
      toWalletId: `user-wallet-${userId}`,
      fromUserId: null,
      toUserId: userId,
      status: 'COMPLETED',
      transactionHash: `onramp-${sessionId}`,
      internalTxId: transactionId,
      description: `USDC purchase via Stripe onramp (Session: ${sessionId})`,
      adminUserId: null,
      metadata: {
        sessionId: sessionId,
        relatedMVTTransaction: mvtTransactionId,
        currency: 'USDC',
        source: 'stripe_onramp',
        transferType: 'onramp_purchase'
      },
      createdAt: now,
      updatedAt: now
    };

    await transactionService.createMVTWalletTransaction(usdcTransactionData);

    return { transactionId: transactionId };
  } catch (error) {
    console.error('Error creating USDC transaction record:', error);
    return null;
  }
}

module.exports = {
  processOnrampTransfer,
  checkOnrampTransferStatus,
  verifyOnrampSession
};
