/**
 * Transaction Resolvers Test Suite
 * Tests for GraphQL field mapping and function routing for transaction operations
 */

const transactionResolvers = require('../transaction.resolvers');
const transactionHandlers = require('../transaction.handlers');

// Mock transaction handlers
jest.mock('../transaction.handlers', () => ({
  handleAdminMintMVT: jest.fn(),
  handleAdminTransferMVT: jest.fn(),
  handleUserTransferMVT: jest.fn(),
  handleGetMVTWalletTransactionList: jest.fn(),
  handleGetMVTWalletTransactionById: jest.fn()
}));

describe('Transaction Resolvers', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('Resolver Structure', () => {
    test('should export all required resolver functions', () => {
      expect(typeof transactionResolvers.adminMintMVT).toBe('function');
      expect(typeof transactionResolvers.adminTransferMVT).toBe('function');
      expect(typeof transactionResolvers.userTransferMVT).toBe('function');
      expect(typeof transactionResolvers.getMVTWalletTransactionList).toBe('function');
      expect(typeof transactionResolvers.getMVTWalletTransactionById).toBe('function');
    });

    test('should map resolvers to correct handlers', () => {
      expect(transactionResolvers.adminMintMVT).toBe(transactionHandlers.handleAdminMintMVT);
      expect(transactionResolvers.adminTransferMVT).toBe(transactionHandlers.handleAdminTransferMVT);
      expect(transactionResolvers.userTransferMVT).toBe(transactionHandlers.handleUserTransferMVT);
      expect(transactionResolvers.getMVTWalletTransactionList).toBe(transactionHandlers.handleGetMVTWalletTransactionList);
      expect(transactionResolvers.getMVTWalletTransactionById).toBe(transactionHandlers.handleGetMVTWalletTransactionById);
    });

    test('should not export any unexpected functions', () => {
      const expectedKeys = [
        'adminMintMVT',
        'adminTransferMVT',
        'userTransferMVT',
        'getMVTWalletTransactionList',
        'getMVTWalletTransactionById'
      ];
      const actualKeys = Object.keys(transactionResolvers);
      
      expect(actualKeys.sort()).toEqual(expectedKeys.sort());
    });
  });

  describe('Admin Operation Resolvers', () => {
    test('should call adminMintMVT handler with correct parameters', async () => {
      // Arrange
      const mockEvent = global.testUtils.createMockEvent('admin-cognito-id', true);
      const mockArgs = global.testUtils.createMockArgs({
        amount: 1000,
        description: 'Test mint'
      });
      const mockResponse = {
        statusCode: 200,
        message: 'Successfully minted 1000 MVT tokens',
        data: global.testUtils.createMockTransaction('mint-123', 'ADMIN_MINT', 1000)
      };
      
      transactionHandlers.handleAdminMintMVT.mockResolvedValue(mockResponse);

      // Act
      const result = await transactionResolvers.adminMintMVT(mockEvent, mockArgs);

      // Assert
      expect(transactionHandlers.handleAdminMintMVT).toHaveBeenCalledWith(mockEvent, mockArgs);
      expect(result).toEqual(mockResponse);
    });

    test('should call adminTransferMVT handler with correct parameters', async () => {
      // Arrange
      const mockEvent = global.testUtils.createMockEvent('admin-cognito-id', true);
      const mockArgs = global.testUtils.createMockArgs({
        userId: 'test-user-123',
        amount: 500,
        description: 'Admin transfer'
      });
      const mockResponse = {
        statusCode: 200,
        message: 'Successfully transferred 500 MVT tokens to user',
        data: global.testUtils.createMockTransaction('transfer-123', 'CENTRAL_TO_USER_TRANSFER', 500)
      };
      
      transactionHandlers.handleAdminTransferMVT.mockResolvedValue(mockResponse);

      // Act
      const result = await transactionResolvers.adminTransferMVT(mockEvent, mockArgs);

      // Assert
      expect(transactionHandlers.handleAdminTransferMVT).toHaveBeenCalledWith(mockEvent, mockArgs);
      expect(result).toEqual(mockResponse);
    });

    test('should propagate admin handler errors', async () => {
      // Arrange
      const mockEvent = global.testUtils.createMockEvent('admin-cognito-id', true);
      const mockArgs = global.testUtils.createMockArgs({ amount: 1000 });
      const mockError = new Error('Admin operation failed');
      
      transactionHandlers.handleAdminMintMVT.mockRejectedValue(mockError);

      // Act & Assert
      await expect(
        transactionResolvers.adminMintMVT(mockEvent, mockArgs)
      ).rejects.toThrow('Admin operation failed');
    });
  });

  describe('User Operation Resolvers', () => {
    test('should call userTransferMVT handler with correct parameters', async () => {
      // Arrange
      const mockEvent = global.testUtils.createMockEvent('user-cognito-id', false);
      const mockArgs = global.testUtils.createMockArgs({
        recipientUserId: 'recipient-123',
        amount: 200,
        description: 'User transfer'
      });
      const mockResponse = {
        statusCode: 200,
        message: 'Successfully transferred 200 MVT tokens',
        data: global.testUtils.createMockTransaction('user-transfer-123', 'USER_TO_USER_TRANSFER', 200)
      };
      
      transactionHandlers.handleUserTransferMVT.mockResolvedValue(mockResponse);

      // Act
      const result = await transactionResolvers.userTransferMVT(mockEvent, mockArgs);

      // Assert
      expect(transactionHandlers.handleUserTransferMVT).toHaveBeenCalledWith(mockEvent, mockArgs);
      expect(result).toEqual(mockResponse);
    });

    test('should propagate user handler errors', async () => {
      // Arrange
      const mockEvent = global.testUtils.createMockEvent('user-cognito-id', false);
      const mockArgs = global.testUtils.createMockArgs({
        recipientUserId: 'recipient-123',
        amount: 200
      });
      const mockError = new Error('Insufficient balance');
      
      transactionHandlers.handleUserTransferMVT.mockRejectedValue(mockError);

      // Act & Assert
      await expect(
        transactionResolvers.userTransferMVT(mockEvent, mockArgs)
      ).rejects.toThrow('Insufficient balance');
    });
  });

  describe('Query Operation Resolvers', () => {
    test('should call getMVTWalletTransactionList handler with correct parameters', async () => {
      // Arrange
      const mockEvent = global.testUtils.createMockEvent('user-cognito-id', false);
      const mockArgs = {
        address: 'test-user-123',
        isAdmin: false,
        limit: 10
      };
      const mockResponse = {
        statusCode: 200,
        message: 'Transactions retrieved successfully',
        data: [
          global.testUtils.createMockTransaction('tx-1', 'ADMIN_MINT', 100),
          global.testUtils.createMockTransaction('tx-2', 'USER_TO_USER_TRANSFER', 50)
        ]
      };
      
      transactionHandlers.handleGetMVTWalletTransactionList.mockResolvedValue(mockResponse);

      // Act
      const result = await transactionResolvers.getMVTWalletTransactionList(mockEvent, mockArgs);

      // Assert
      expect(transactionHandlers.handleGetMVTWalletTransactionList).toHaveBeenCalledWith(mockEvent, mockArgs);
      expect(result).toEqual(mockResponse);
    });

    test('should call getMVTWalletTransactionById handler with correct parameters', async () => {
      // Arrange
      const mockEvent = global.testUtils.createMockEvent('user-cognito-id', false);
      const mockArgs = { id: 'test-tx-123' };
      const mockResponse = {
        statusCode: 200,
        message: 'Transaction retrieved successfully',
        data: global.testUtils.createMockTransaction('test-tx-123', 'ADMIN_MINT', 100)
      };
      
      transactionHandlers.handleGetMVTWalletTransactionById.mockResolvedValue(mockResponse);

      // Act
      const result = await transactionResolvers.getMVTWalletTransactionById(mockEvent, mockArgs);

      // Assert
      expect(transactionHandlers.handleGetMVTWalletTransactionById).toHaveBeenCalledWith(mockEvent, mockArgs);
      expect(result).toEqual(mockResponse);
    });

    test('should handle transaction not found scenarios', async () => {
      // Arrange
      const mockEvent = global.testUtils.createMockEvent('user-cognito-id', false);
      const mockArgs = { id: 'non-existent-tx' };
      const mockResponse = {
        statusCode: 404,
        message: 'Transaction not found'
      };
      
      transactionHandlers.handleGetMVTWalletTransactionById.mockResolvedValue(mockResponse);

      // Act
      const result = await transactionResolvers.getMVTWalletTransactionById(mockEvent, mockArgs);

      // Assert
      expect(result.statusCode).toBe(404);
      expect(result.message).toContain('not found');
    });
  });

  describe('Resolver Integration', () => {
    test('should maintain function signatures for GraphQL compatibility', () => {
      // Test that resolvers accept the standard GraphQL resolver signature
      const mockParent = {};
      const mockArgs = global.testUtils.createMockArgs({ amount: 100 });
      const mockContext = {};
      const mockInfo = {};

      // These should not throw errors when called with GraphQL resolver signature
      expect(() => {
        transactionResolvers.adminMintMVT(mockParent, mockArgs, mockContext, mockInfo);
      }).not.toThrow();

      expect(() => {
        transactionResolvers.userTransferMVT(mockParent, mockArgs, mockContext, mockInfo);
      }).not.toThrow();

      expect(() => {
        transactionResolvers.getMVTWalletTransactionList(mockParent, mockArgs, mockContext, mockInfo);
      }).not.toThrow();
    });

    test('should handle concurrent resolver calls', async () => {
      // Arrange
      const mockEvent1 = global.testUtils.createMockEvent('admin-1', true);
      const mockEvent2 = global.testUtils.createMockEvent('user-1', false);
      const mockEvent3 = global.testUtils.createMockEvent('user-2', false);

      const mintArgs = global.testUtils.createMockArgs({ amount: 1000, description: 'Mint' });
      const transferArgs = global.testUtils.createMockArgs({ recipientUserId: 'recipient', amount: 100 });
      const listArgs = { address: 'user-1', isAdmin: false };

      transactionHandlers.handleAdminMintMVT.mockResolvedValue({
        statusCode: 200,
        data: global.testUtils.createMockTransaction('mint-123', 'ADMIN_MINT', 1000)
      });
      
      transactionHandlers.handleUserTransferMVT.mockResolvedValue({
        statusCode: 200,
        data: global.testUtils.createMockTransaction('transfer-123', 'USER_TO_USER_TRANSFER', 100)
      });
      
      transactionHandlers.handleGetMVTWalletTransactionList.mockResolvedValue({
        statusCode: 200,
        data: []
      });

      // Act
      const [mintResult, transferResult, listResult] = await Promise.all([
        transactionResolvers.adminMintMVT(mockEvent1, mintArgs),
        transactionResolvers.userTransferMVT(mockEvent2, transferArgs),
        transactionResolvers.getMVTWalletTransactionList(mockEvent3, listArgs)
      ]);

      // Assert
      expect(mintResult.statusCode).toBe(200);
      expect(transferResult.statusCode).toBe(200);
      expect(listResult.statusCode).toBe(200);
      expect(transactionHandlers.handleAdminMintMVT).toHaveBeenCalledWith(mockEvent1, mintArgs);
      expect(transactionHandlers.handleUserTransferMVT).toHaveBeenCalledWith(mockEvent2, transferArgs);
      expect(transactionHandlers.handleGetMVTWalletTransactionList).toHaveBeenCalledWith(mockEvent3, listArgs);
    });
  });

  describe('Error Handling', () => {
    test('should handle handler timeout errors', async () => {
      // Arrange
      const mockEvent = global.testUtils.createMockEvent('admin-cognito-id', true);
      const mockArgs = global.testUtils.createMockArgs({ amount: 1000 });
      const timeoutError = new Error('timeout');
      
      transactionHandlers.handleAdminMintMVT.mockRejectedValue(timeoutError);

      // Act & Assert
      await expect(
        transactionResolvers.adminMintMVT(mockEvent, mockArgs)
      ).rejects.toThrow('timeout');
    });

    test('should handle handler validation errors', async () => {
      // Arrange
      const mockEvent = global.testUtils.createMockEvent('user-cognito-id', false);
      const mockArgs = global.testUtils.createMockArgs({ recipientUserId: '', amount: 100 });
      const validationError = new Error('Recipient user ID is required');
      
      transactionHandlers.handleUserTransferMVT.mockRejectedValue(validationError);

      // Act & Assert
      await expect(
        transactionResolvers.userTransferMVT(mockEvent, mockArgs)
      ).rejects.toThrow('Recipient user ID is required');
    });

    test('should handle handler authorization errors', async () => {
      // Arrange
      const mockEvent = global.testUtils.createMockEvent('user-cognito-id', false);
      const mockArgs = global.testUtils.createMockArgs({ amount: 1000 });
      const authError = new Error('Unauthorized: Admin access required');
      
      transactionHandlers.handleAdminMintMVT.mockRejectedValue(authError);

      // Act & Assert
      await expect(
        transactionResolvers.adminMintMVT(mockEvent, mockArgs)
      ).rejects.toThrow('Unauthorized: Admin access required');
    });

    test('should handle undefined event gracefully', async () => {
      // Arrange
      const mockArgs = global.testUtils.createMockArgs({ amount: 1000 });
      transactionHandlers.handleAdminMintMVT.mockResolvedValue({
        statusCode: 400,
        message: 'Invalid request'
      });

      // Act
      const result = await transactionResolvers.adminMintMVT(undefined, mockArgs);

      // Assert
      expect(transactionHandlers.handleAdminMintMVT).toHaveBeenCalledWith(undefined, mockArgs);
      expect(result.statusCode).toBe(400);
    });

    test('should handle undefined args gracefully', async () => {
      // Arrange
      const mockEvent = global.testUtils.createMockEvent('admin-cognito-id', true);
      transactionHandlers.handleAdminMintMVT.mockResolvedValue({
        statusCode: 400,
        message: 'Invalid request'
      });

      // Act
      const result = await transactionResolvers.adminMintMVT(mockEvent, undefined);

      // Assert
      expect(transactionHandlers.handleAdminMintMVT).toHaveBeenCalledWith(mockEvent, undefined);
      expect(result.statusCode).toBe(400);
    });
  });

  describe('Performance and Memory', () => {
    test('should not create new function instances on each call', () => {
      // Arrange & Act
      const resolver1 = transactionResolvers.adminMintMVT;
      const resolver2 = transactionResolvers.adminMintMVT;

      // Assert
      expect(resolver1).toBe(resolver2);
      expect(resolver1).toBe(transactionHandlers.handleAdminMintMVT);
    });

    test('should handle rapid successive calls', async () => {
      // Arrange
      const mockEvent = global.testUtils.createMockEvent('admin-cognito-id', true);
      const mockArgs = global.testUtils.createMockArgs({ amount: 100, description: 'Test' });
      const mockResponse = { statusCode: 200, data: {} };
      
      transactionHandlers.handleAdminMintMVT.mockResolvedValue(mockResponse);

      // Act
      const promises = Array(10).fill().map(() => 
        transactionResolvers.adminMintMVT(mockEvent, mockArgs)
      );
      
      const results = await Promise.all(promises);

      // Assert
      expect(results).toHaveLength(10);
      expect(results.every(result => result.statusCode === 200)).toBe(true);
      expect(transactionHandlers.handleAdminMintMVT).toHaveBeenCalledTimes(10);
    });
  });
});
