/**
 * Admin Workflow Scenario Test Suite
 * Tests for admin workflows: Setup → Monitor → Approve Swaps
 */

// Create mock DynamoDB instance
const mockDynamoDB = {
  getItem: jest.fn(),
  putItem: jest.fn(),
  updateItem: jest.fn(),
  scan: jest.fn(),
  query: jest.fn(),
  describeTable: jest.fn(),
  batchGetItem: jest.fn(),
  batchWriteItem: jest.fn()
};

// Mock all dependencies
jest.mock('../../config/aws', () => ({
  AWS: {
    DynamoDB: {
      Converter: {
        marshall: jest.fn((item) => item),
        unmarshall: jest.fn((item) => item)
      }
    }
  },
  ddb: mockDynamoDB
}));

jest.mock('../../shared/database/dynamoUtils', () => ({
  getTableName: jest.fn((tableName) => `test-${tableName}`),
  tableExists: jest.fn().mockResolvedValue(true)
}));

jest.mock('../../shared/services/authService', () => ({
  checkAdminAuthorization: jest.fn(),
  getCurrentUserDatabaseId: jest.fn(),
  getUserIdFromEvent: jest.fn(),
  checkUserAuthorization: jest.fn()
}));

jest.mock('../../shared/utils/validationUtils', () => ({
  validateMintInput: jest.fn().mockReturnValue({ isValid: true }),
  validateTransferInput: jest.fn().mockReturnValue({ isValid: true }),
  validateUserTransferInput: jest.fn().mockReturnValue({ isValid: true }),
  validateUSDCDepositInput: jest.fn().mockReturnValue({ isValid: true }),
  validateSwapRequestInput: jest.fn().mockReturnValue({ isValid: true }),
  validateSwapApprovalInput: jest.fn().mockReturnValue({ isValid: true }),
  validateSwapRejectionInput: jest.fn().mockReturnValue({ isValid: true }),
  validateMVTAmount: jest.fn().mockReturnValue({ isValid: true }),
  validateUSDCAmount: jest.fn().mockReturnValue({ isValid: true }),
  validateUserId: jest.fn().mockReturnValue({ isValid: true }),
  validateWalletAddress: jest.fn().mockReturnValue({ isValid: true })
}));

jest.mock('../../modules/wallet/wallet.service', () => ({
  getCentralWalletBalance: jest.fn().mockResolvedValue({
    id: 'central-mvt-wallet',
    balance: 50000,
    totalMinted: 100000,
    totalTransferred: 50000,
    lastMintedAt: '2024-01-01T00:00:00.000Z',
    createdAt: '2024-01-01T00:00:00.000Z'
  }),
  getUserBalance: jest.fn().mockResolvedValue({
    userId: 'test-user-123',
    balance: 1000,
    pendingBalance: 0,
    lockedBalance: 0,
    availableBalance: 1000,
    totalReceived: 1000,
    totalSent: 0,
    lastUpdated: '2024-01-01T00:00:00.000Z',
    recentTransactions: []
  }),
  updateCentralWalletBalance: jest.fn().mockResolvedValue(true),
  updateUserBalance: jest.fn().mockResolvedValue(true),
  lockUserMVTTokens: jest.fn().mockResolvedValue(true),
  unlockUserMVTTokens: jest.fn().mockResolvedValue(true),
  transferLockedMVTToCentral: jest.fn().mockResolvedValue(true)
}));

jest.mock('../../shared/blockchain/contractService', () => ({
  getContractUSDCBalance: jest.fn().mockResolvedValue('10000000000'), // 10,000 USDC
  getWalletAddress: jest.fn().mockReturnValue('******************************************'),
  isBlockchainConnected: jest.fn().mockReturnValue(true),
  depositUSDCToContract: jest.fn().mockResolvedValue({
    transactionHash: '0xabcdef123456',
    blockNumber: 12345,
    gasUsed: '21000',
    status: 1,
    approvalNeeded: false,
    approvalTxHash: null
  }),
  withdrawUSDCFromContract: jest.fn().mockResolvedValue({
    transactionHash: '0xfedcba654321',
    blockNumber: 12346,
    gasUsed: '25000',
    status: 1
  }),
  transferUSDCToUser: jest.fn().mockResolvedValue({
    transactionHash: '0xfedcba654321',
    blockNumber: 12346,
    gasUsed: '25000',
    status: 1
  })
}));

// Mock constants globally to ensure they're available everywhere
const mockConstants = {
  CENTRAL_WALLET_ID: 'central-mvt-wallet',
  TOKEN_TYPES: {
    MVT: 'MVT',
    USDC: 'USDC'
  },
  TRANSACTION_TYPES: {
    ADMIN_MINT: 'ADMIN_MINT',
    CENTRAL_TO_USER_TRANSFER: 'CENTRAL_TO_USER_TRANSFER',
    USER_TO_USER_TRANSFER: 'USER_TO_USER_TRANSFER',
    USDC_DEPOSIT: 'USDC_DEPOSIT',
    USDC_WITHDRAWAL: 'USDC_WITHDRAWAL'
  },
  TRANSACTION_STATUS: {
    COMPLETED: 'COMPLETED',
    PENDING: 'PENDING',
    FAILED: 'FAILED'
  },
  SWAP_STATUS: {
    PENDING: 'PENDING',
    APPROVED: 'APPROVED',
    REJECTED: 'REJECTED'
  },
  STATUS_CODES: {
    SUCCESS: 200,
    BAD_REQUEST: 400,
    UNAUTHORIZED: 401,
    FORBIDDEN: 403,
    NOT_FOUND: 404,
    INTERNAL_ERROR: 500
  }
};

// Make constants available globally
global.TOKEN_TYPES = mockConstants.TOKEN_TYPES;
global.TRANSACTION_TYPES = mockConstants.TRANSACTION_TYPES;
global.TRANSACTION_STATUS = mockConstants.TRANSACTION_STATUS;
global.SWAP_STATUS = mockConstants.SWAP_STATUS;
global.STATUS_CODES = mockConstants.STATUS_CODES;
global.CENTRAL_WALLET_ID = mockConstants.CENTRAL_WALLET_ID;

jest.mock('../../shared/constants/index', () => mockConstants);

// Import modules after mocks are set up
const walletHandlers = require('../../modules/wallet/wallet.handlers');
const transactionHandlers = require('../../modules/transaction/transaction.handlers');
const usdcHandlers = require('../../modules/usdc/usdc.handlers');
const exchangeRateHandlers = require('../../modules/exchangeRate/exchangeRate.handlers');
const swapHandlers = require('../../modules/swap/swap.handlers');
const authService = require('../../shared/services/authService');

// Set up global test utilities
global.testUtils = {
  // Mock GraphQL event
  createMockEvent: (cognitoIdentityId = 'test-cognito-id', isAdmin = false) => ({
    requestContext: {
      identity: {
        cognitoIdentityId
      }
    },
    arguments: {},
    info: {
      fieldName: 'testField'
    },
    source: {},
    stateValues: {},
    prev: null
  }),

  // Mock GraphQL arguments
  createMockArgs: (input = {}) => ({
    input
  }),

  // Mock user data
  createMockUser: (userId = 'test-user-123', isAdmin = false) => ({
    id: userId,
    cognitoId: 'test-cognito-id',
    email: '<EMAIL>',
    firstName: 'Test',
    lastName: 'User',
    walletAddress: isAdmin ? null : '******************************************',
    role: isAdmin ? 'SUPER_ADMIN' : 'MEMBER',
    isDeleted: 'false',
    createdAt: '2024-01-01T00:00:00.000Z',
    updatedAt: '2024-01-01T00:00:00.000Z'
  }),

  // Mock swap request
  createMockSwapRequest: (id = 'test-swap-123', status = 'PENDING') => ({
    id,
    userId: 'test-user-123',
    mvtAmount: 100,
    usdcAmount: 50.0,
    exchangeRate: 0.5,
    status,
    userWalletAddress: '******************************************',
    description: 'Test swap request',
    requestedAt: '2024-01-01T00:00:00.000Z',
    processedAt: status !== 'PENDING' ? '2024-01-01T01:00:00.000Z' : null,
    adminUserId: status !== 'PENDING' ? 'admin-user-123' : null,
    transactionHash: status === 'APPROVED' ? '0xabcdef' : null,
    isDeleted: 'false',
    createdAt: '2024-01-01T00:00:00.000Z',
    updatedAt: '2024-01-01T00:00:00.000Z'
  }),

  // Mock DynamoDB responses
  mockDynamoDBSuccess: (data = {}) => ({
    promise: jest.fn().mockResolvedValue(data)
  }),

  mockDynamoDBError: (error = new Error('DynamoDB error')) => ({
    promise: jest.fn().mockRejectedValue(error)
  })
};

describe('Admin Workflow Scenario Tests', () => {
  beforeEach(() => {
    jest.clearAllMocks();

    // Mock environment variables for blockchain connectivity
    process.env.ETHEREUM_RPC_URL = 'http://localhost:8545';
    process.env.PRIVATE_KEY = '******************************************123456789012345678901234';
    process.env.MVT_WITHDRAW_CONTRACT_ADDRESS = '******************************************';
    process.env.USDC_TOKEN_ADDRESS = '******************************************';

    // Setup default DynamoDB responses
    mockDynamoDB.getItem.mockReturnValue(global.testUtils.mockDynamoDBSuccess({
      Item: { id: 'central-mvt-wallet', mvtBalance: 20000, lockedMVT: 0 }
    }));
    mockDynamoDB.putItem.mockReturnValue(global.testUtils.mockDynamoDBSuccess());
    mockDynamoDB.updateItem.mockReturnValue(global.testUtils.mockDynamoDBSuccess());
    mockDynamoDB.scan.mockReturnValue(global.testUtils.mockDynamoDBSuccess({ Items: [] }));

    // Setup auth service defaults
    authService.checkAdminAuthorization.mockResolvedValue(true);
    authService.getCurrentUserDatabaseId.mockResolvedValue('admin-user-123');
    authService.getUserIdFromEvent.mockResolvedValue('admin-cognito-123');
    authService.checkUserAuthorization.mockResolvedValue(true);
  });

  describe('Admin Workflow: Setup → Monitor → Approve Swaps', () => {
    test('should complete admin workflow successfully', async () => {
      const adminUserId = 'admin-user-123';
      const swapRequestId = 'swap-123';

      // Mock admin authentication
      authService.checkAdminAuthorization.mockResolvedValue(true);
      authService.getCurrentUserDatabaseId.mockResolvedValue(adminUserId);
      authService.getUserIdFromEvent.mockResolvedValue('admin-cognito-123');

      // Step 1: Admin deposits USDC to liquidity pool
      const depositEvent = global.testUtils.createMockEvent('admin-cognito-id', true);
      const depositArgs = global.testUtils.createMockArgs({
        amount: 5000.0,
        description: 'Initial liquidity deposit'
      });

      const depositResult = await usdcHandlers.handleAdminDepositUSDC(depositEvent, depositArgs);

      expect(depositResult.statusCode).toBe(200);
      expect(depositResult.data.transactionType).toBe('USDC_DEPOSIT');

      // Step 2: Admin mints MVT tokens
      const mintEvent = global.testUtils.createMockEvent('admin-cognito-id', true);
      const mintArgs = global.testUtils.createMockArgs({
        amount: 10000,
        description: 'Initial token mint'
      });

      const mintResult = await transactionHandlers.handleAdminMintMVT(mintEvent, mintArgs);

      expect(mintResult.statusCode).toBe(200);
      expect(mintResult.data.transactionType).toBe('ADMIN_MINT');

      // Step 3: Admin checks exchange rate
      const rateEvent = global.testUtils.createMockEvent('admin-cognito-id', true);
      const rateArgs = global.testUtils.createMockArgs();

      // Mock central wallet for exchange rate calculation
      mockDynamoDB.getItem.mockReturnValue(global.testUtils.mockDynamoDBSuccess({
        Item: { id: 'central-mvt-wallet', mvtBalance: 20000, lockedMVT: 0 }
      }));

      const rateResult = await exchangeRateHandlers.handleGetMVTWalletExchangeRate(rateEvent, rateArgs);

      expect(rateResult.statusCode).toBe(200);
      expect(rateResult.data.currentRate).toBeDefined();

      // Step 4: Admin views pending swap requests
      const swapListEvent = global.testUtils.createMockEvent('admin-cognito-id', true);
      const swapListArgs = { isAdmin: true };

      mockDynamoDB.scan.mockReturnValue(global.testUtils.mockDynamoDBSuccess({
        Items: [global.testUtils.createMockSwapRequest(swapRequestId, 'PENDING')]
      }));

      const swapListResult = await swapHandlers.handleGetMVTWalletSwapRequests(swapListEvent, swapListArgs);

      expect(swapListResult.statusCode).toBe(200);
      expect(swapListResult.data).toHaveLength(1);

      // Step 5: Admin approves swap request
      const approveEvent = global.testUtils.createMockEvent('admin-cognito-id', true);
      const approveArgs = global.testUtils.createMockArgs({
        swapRequestId: swapRequestId
      });

      mockDynamoDB.getItem.mockReturnValue(global.testUtils.mockDynamoDBSuccess({
        Item: global.testUtils.createMockSwapRequest(swapRequestId, 'PENDING')
      }));

      const approveResult = await swapHandlers.handleApproveMVTWalletSwap(approveEvent, approveArgs);

      expect(approveResult.statusCode).toBe(200);
      expect(approveResult.data.status).toBe('APPROVED');
    });

    test('should handle admin workflow with unauthorized access', async () => {
      // Mock non-admin user trying admin operations
      authService.checkAdminAuthorization.mockResolvedValue(false);

      const userEvent = global.testUtils.createMockEvent('user-cognito-id', false);
      const mintArgs = global.testUtils.createMockArgs({
        amount: 1000,
        description: 'Unauthorized mint attempt'
      });

      const result = await transactionHandlers.handleAdminMintMVT(userEvent, mintArgs);

      expect(result.statusCode).toBe(403);
    });
  });
});
