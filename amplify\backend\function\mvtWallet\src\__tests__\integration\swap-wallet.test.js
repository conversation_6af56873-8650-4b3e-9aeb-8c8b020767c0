/**
 * Swap and Wallet Module Integration Test Suite
 * Tests for interactions between swap and wallet modules
 */

// Create mock DynamoDB instance
const mockDynamoDB = {
  getItem: jest.fn(),
  putItem: jest.fn(),
  updateItem: jest.fn(),
  scan: jest.fn(),
  query: jest.fn(),
  describeTable: jest.fn(),
  batchGetItem: jest.fn(),
  batchWriteItem: jest.fn()
};

const mockData = require('../shared/mockData');

// Mock AWS and shared utilities
jest.mock('../../config/aws', () => ({
  AWS: {
    DynamoDB: {
      Converter: {
        marshall: jest.fn((item) => item),
        unmarshall: jest.fn((item) => item)
      }
    }
  },
  ddb: mockDynamoDB
}));

const swapService = require('../../modules/swap/swap.service');
const walletService = require('../../modules/wallet/wallet.service');

jest.mock('../../shared/database/dynamoUtils', () => ({
  getTableName: jest.fn((tableName) => `test-${tableName}`),
  tableExists: jest.fn().mockResolvedValue(true)
}));

jest.mock('../../shared/blockchain/contractService', () => ({
  getContractUSDCBalance: jest.fn().mockResolvedValue('10000000000'), // 10,000 USDC
  getWalletAddress: jest.fn().mockReturnValue('******************************************'),
  isBlockchainConnected: jest.fn().mockReturnValue(true),
  transferUSDCToUser: jest.fn().mockResolvedValue(
    mockData.createMockBlockchainTransaction('transfer')
  )
}));

// Create stateful mocks for wallet service to track locked balances
let mockUserLockedBalance = 0;
let mockUserBalance = 1000;

const mockWalletServiceWithState = {
  getCentralWalletBalance: jest.fn().mockResolvedValue(mockData.TEST_WALLETS.CENTRAL),
  getUserBalance: jest.fn().mockImplementation((userId) => {
    return Promise.resolve({
      userId: userId,
      balance: mockUserBalance,
      pendingBalance: 0,
      lockedBalance: mockUserLockedBalance,
      availableBalance: mockUserBalance - mockUserLockedBalance,
      totalReceived: mockUserBalance,
      totalSent: 0,
      lastUpdated: '2024-01-01T00:00:00.000Z',
      recentTransactions: []
    });
  }),
  updateCentralWalletBalance: jest.fn().mockResolvedValue(true),
  updateUserBalance: jest.fn().mockResolvedValue(true),
  lockUserMVTTokens: jest.fn().mockImplementation((userId, amount) => {
    mockUserLockedBalance += amount;
    return Promise.resolve(true);
  }),
  unlockUserMVTTokens: jest.fn().mockImplementation((userId, amount) => {
    mockUserLockedBalance = Math.max(0, mockUserLockedBalance - amount);
    return Promise.resolve(true);
  }),
  transferLockedMVTToCentral: jest.fn().mockImplementation((userId, amount) => {
    mockUserLockedBalance = Math.max(0, mockUserLockedBalance - amount);
    mockUserBalance = Math.max(0, mockUserBalance - amount);
    return Promise.resolve(true);
  })
};

jest.mock('../../modules/wallet/wallet.service', () => ({
  getCentralWalletBalance: jest.fn().mockResolvedValue({
    id: 'central-mvt-wallet',
    balance: 50000,
    totalMinted: 100000,
    totalTransferred: 50000,
    lastMintedAt: '2024-01-01T00:00:00.000Z',
    createdAt: '2024-01-01T00:00:00.000Z'
  }),
  getUserBalance: jest.fn().mockResolvedValue({
    userId: 'test-user-123',
    balance: 1000,
    pendingBalance: 0,
    lockedBalance: 0,
    availableBalance: 1000,
    totalReceived: 1000,
    totalSent: 0,
    lastUpdated: '2024-01-01T00:00:00.000Z',
    recentTransactions: []
  }),
  updateCentralWalletBalance: jest.fn().mockResolvedValue(true),
  updateUserBalance: jest.fn().mockResolvedValue(true),
  lockUserMVTTokens: jest.fn().mockResolvedValue(true),
  unlockUserMVTTokens: jest.fn().mockResolvedValue(true),
  transferLockedMVTToCentral: jest.fn().mockResolvedValue(true)
}));

// Make constants available globally
global.TOKEN_TYPES = mockData.MOCK_CONSTANTS.TOKEN_TYPES;
global.TRANSACTION_TYPES = mockData.MOCK_CONSTANTS.TRANSACTION_TYPES;
global.TRANSACTION_STATUS = mockData.MOCK_CONSTANTS.TRANSACTION_STATUS;
global.SWAP_STATUS = mockData.MOCK_CONSTANTS.SWAP_STATUS;
global.STATUS_CODES = mockData.MOCK_CONSTANTS.STATUS_CODES;
global.CENTRAL_WALLET_ID = mockData.MOCK_CONSTANTS.CENTRAL_WALLET_ID;

jest.mock('../../shared/constants/index', () => mockData.MOCK_CONSTANTS);

// Set up global test utilities
global.testUtils = {
  createMockEvent: mockData.createMockEvent,
  createMockArgs: mockData.createMockArgs,
  createMockUser: mockData.createMockUser,
  createMockSwapRequest: mockData.createMockSwapRequest,
  createMockWalletBalance: mockData.createMockWalletBalance,
  mockDynamoDBSuccess: mockData.mockDynamoDBSuccess,
  mockDynamoDBError: mockData.mockDynamoDBError
};

describe('Swap and Wallet Module Integration Tests', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    
    // Reset stateful variables
    mockUserLockedBalance = 0;
    mockUserBalance = 1000;
    
    // Setup default successful DynamoDB responses
    mockDynamoDB.getItem.mockReturnValue(mockData.mockDynamoDBSuccess({
      Item: mockData.TEST_WALLETS.USER_WITH_BALANCE
    }));
    mockDynamoDB.putItem.mockReturnValue(mockData.mockDynamoDBSuccess());
    mockDynamoDB.updateItem.mockReturnValue(mockData.mockDynamoDBSuccess());
    mockDynamoDB.scan.mockReturnValue(mockData.mockDynamoDBSuccess({ Items: [] }));
  });

  describe('Swap Request Creation Integration', () => {
    test('should create swap request and lock tokens atomically', async () => {
      // Arrange
      const userId = 'test-user-123';
      const mvtAmount = 100;
      const description = 'Integration test swap';

      // Mock user lookup and central wallet for exchange rate
      mockDynamoDB.getItem
        .mockReturnValueOnce(mockData.mockDynamoDBSuccess({
          Item: mockData.TEST_USERS.REGULAR_USER
        }))
        .mockReturnValueOnce(mockData.mockDynamoDBSuccess({
          Item: mockData.TEST_WALLETS.CENTRAL
        }))
        .mockReturnValueOnce(mockData.mockDynamoDBSuccess({
          Item: { id: `user-wallet-${userId}`, mvtBalance: 1000, lockedMVT: 0 }
        }));

      // Act
      const swapRequest = await swapService.createSwapRequest(userId, mvtAmount, description);

      // Assert
      expect(swapRequest.status).toBe('PENDING');
      expect(swapRequest.mvtAmount).toBe(mvtAmount);
      expect(swapRequest.userId).toBe(userId);
      
      // Verify tokens were locked
      expect(mockWalletServiceWithState.lockUserMVTTokens).toHaveBeenCalledWith(userId, mvtAmount);
      expect(mockUserLockedBalance).toBe(mvtAmount);
      
      // Verify swap request was saved
      expect(mockDynamoDB.putItem).toHaveBeenCalledWith(
        expect.objectContaining({
          TableName: 'test-MVTSwapRequest',
          Item: expect.objectContaining({
            userId: userId,
            mvtAmount: mvtAmount,
            status: 'PENDING'
          })
        })
      );
    });

    test('should handle insufficient balance during swap request creation', async () => {
      // Arrange
      const userId = 'test-user-123';
      const mvtAmount = 1500; // More than available balance
      mockUserBalance = 1000;

      mockDynamoDB.getItem.mockReturnValue(mockData.mockDynamoDBSuccess({
        Item: mockData.TEST_USERS.REGULAR_USER
      }));

      // Act & Assert
      await expect(
        swapService.createSwapRequest(userId, mvtAmount, 'test')
      ).rejects.toThrow('Insufficient MVT balance');
      
      // Verify no tokens were locked
      expect(mockWalletServiceWithState.lockUserMVTTokens).not.toHaveBeenCalled();
      expect(mockUserLockedBalance).toBe(0);
    });
  });

  describe('Swap Approval Integration', () => {
    test('should approve swap and transfer locked tokens to central wallet', async () => {
      // Arrange
      const swapRequestId = 'swap-123';
      const adminUserId = 'admin-123';
      const mvtAmount = 100;
      
      // Pre-lock tokens
      mockUserLockedBalance = mvtAmount;
      
      const mockSwapRequest = mockData.createMockSwapRequest(swapRequestId, 'PENDING');
      mockSwapRequest.mvtAmount = mvtAmount;
      mockSwapRequest.userId = 'test-user-123';

      mockDynamoDB.getItem.mockReturnValue(mockData.mockDynamoDBSuccess({
        Item: mockSwapRequest
      }));

      // Act
      const approvedSwap = await swapService.approveSwapRequest(swapRequestId, adminUserId);

      // Assert
      expect(approvedSwap.status).toBe('APPROVED');
      expect(approvedSwap.adminUserId).toBe(adminUserId);
      expect(approvedSwap.transactionHash).toBeDefined();
      
      // Verify locked tokens were transferred to central
      expect(mockWalletServiceWithState.transferLockedMVTToCentral).toHaveBeenCalledWith(
        mockSwapRequest.userId, 
        mvtAmount
      );
      expect(mockUserLockedBalance).toBe(0);
      
      // Verify USDC transfer to user
      const contractService = require('../../shared/blockchain/contractService');
      expect(contractService.transferUSDCToUser).toHaveBeenCalledWith(
        mockSwapRequest.userWalletAddress,
        expect.any(Number) // USDC amount
      );
    });

    test('should handle blockchain failures during swap approval', async () => {
      // Arrange
      const swapRequestId = 'swap-123';
      const adminUserId = 'admin-123';
      
      const mockSwapRequest = mockData.createMockSwapRequest(swapRequestId, 'PENDING');
      mockDynamoDB.getItem.mockReturnValue(mockData.mockDynamoDBSuccess({
        Item: mockSwapRequest
      }));

      // Mock blockchain failure
      const contractService = require('../../shared/blockchain/contractService');
      contractService.transferUSDCToUser.mockRejectedValue(new Error('Blockchain transfer failed'));

      // Act & Assert
      await expect(
        swapService.approveSwapRequest(swapRequestId, adminUserId)
      ).rejects.toThrow('Failed to approve swap request');
    });
  });

  describe('Swap Rejection Integration', () => {
    test('should reject swap and unlock tokens', async () => {
      // Arrange
      const swapRequestId = 'swap-123';
      const adminUserId = 'admin-123';
      const reason = 'Insufficient documentation';
      const mvtAmount = 100;
      
      // Pre-lock tokens
      mockUserLockedBalance = mvtAmount;
      
      const mockSwapRequest = mockData.createMockSwapRequest(swapRequestId, 'PENDING');
      mockSwapRequest.mvtAmount = mvtAmount;
      mockSwapRequest.userId = 'test-user-123';

      mockDynamoDB.getItem
        .mockReturnValueOnce(mockData.mockDynamoDBSuccess({ Item: mockSwapRequest }))
        .mockReturnValueOnce(mockData.mockDynamoDBSuccess({
          Item: { id: `user-wallet-${mockSwapRequest.userId}`, mvtBalance: 1000, lockedMVT: mvtAmount }
        }));

      // Act
      const rejectedSwap = await swapService.rejectSwapRequest(swapRequestId, reason, adminUserId);

      // Assert
      expect(rejectedSwap.status).toBe('REJECTED');
      expect(rejectedSwap.rejectionReason).toBe(reason);
      expect(rejectedSwap.adminUserId).toBe(adminUserId);
      
      // Verify tokens were unlocked
      expect(mockWalletServiceWithState.unlockUserMVTTokens).toHaveBeenCalledWith(
        mockSwapRequest.userId, 
        mvtAmount
      );
      expect(mockUserLockedBalance).toBe(0);
    });
  });

  describe('Locked Balance Management Integration', () => {
    test('should prevent transfers when tokens are locked for swaps', async () => {
      // Arrange
      const userId = 'test-user-123';
      const lockedAmount = 200;
      const transferAmount = 900; // Would exceed available balance
      
      mockUserBalance = 1000;
      mockUserLockedBalance = lockedAmount;

      // Act
      const userBalance = await mockWalletServiceWithState.getUserBalance(userId);

      // Assert
      expect(userBalance.balance).toBe(1000);
      expect(userBalance.lockedBalance).toBe(200);
      expect(userBalance.availableBalance).toBe(800); // 1000 - 200
      
      // Verify transfer would be rejected due to insufficient available balance
      expect(userBalance.availableBalance).toBeLessThan(transferAmount);
    });

    test('should track multiple concurrent swap requests with locked balances', async () => {
      // Arrange
      const userId = 'test-user-123';
      const swap1Amount = 100;
      const swap2Amount = 150;
      
      mockUserBalance = 1000;

      // Act - Create first swap request
      await mockWalletServiceWithState.lockUserMVTTokens(userId, swap1Amount);
      expect(mockUserLockedBalance).toBe(100);

      // Act - Create second swap request
      await mockWalletServiceWithState.lockUserMVTTokens(userId, swap2Amount);
      expect(mockUserLockedBalance).toBe(250);

      // Act - Reject first swap
      await mockWalletServiceWithState.unlockUserMVTTokens(userId, swap1Amount);
      expect(mockUserLockedBalance).toBe(150);

      // Act - Approve second swap
      await mockWalletServiceWithState.transferLockedMVTToCentral(userId, swap2Amount);
      expect(mockUserLockedBalance).toBe(0);
      expect(mockUserBalance).toBe(850); // 1000 - 150
    });
  });
});
