const transactionHandlers = require('./transaction.handlers');

/**
 * GraphQL field resolver mapping for transaction operations
 * Maps GraphQL field names to their corresponding handler functions
 */
const transactionResolvers = {
  // Admin transaction operations
  adminMintMVT: transactionHandlers.handleAdminMintMVT,
  adminTransferMVT: transactionHandlers.handleAdminTransferMVT,
  
  // User transaction operations
  userTransferMVT: transactionHandlers.handleUserTransferMVT,
  
  // Transaction query operations
  getMVTWalletTransactionList: transactionHandlers.handleGetMVTWalletTransactionList,
  getMVTWalletTransactionById: transactionHandlers.handleGetMVTWalletTransactionById
};

module.exports = transactionResolvers;
