/**
 * Wallet Validation Test Suite
 * Tests for wallet input validation functions with valid/invalid inputs and edge cases
 */

const walletValidation = require('../wallet.validation');

describe('Wallet Validation', () => {
  describe('validateBalanceRequest', () => {
    test('should validate empty input (user checking own balance)', () => {
      // Act
      const result = walletValidation.validateBalanceRequest({});

      // Assert
      expect(result.isValid).toBe(true);
    });

    test('should validate null input', () => {
      // Act
      const result = walletValidation.validateBalanceRequest(null);

      // Assert
      expect(result.isValid).toBe(true);
    });

    test('should validate undefined input', () => {
      // Act
      const result = walletValidation.validateBalanceRequest(undefined);

      // Assert
      expect(result.isValid).toBe(true);
    });

    test('should validate valid userId', () => {
      // Act
      const result = walletValidation.validateBalanceRequest({ userId: 'test-user-123' });

      // Assert
      expect(result.isValid).toBe(true);
    });

    test('should reject non-string userId', () => {
      // Act
      const result = walletValidation.validateBalanceRequest({ userId: 123 });

      // Assert
      expect(result.isValid).toBe(false);
      expect(result.error).toBe('User ID must be a string');
    });

    test('should reject empty string userId', () => {
      // Act
      const result = walletValidation.validateBalanceRequest({ userId: '' });

      // Assert
      expect(result.isValid).toBe(false);
      expect(result.error).toBe('User ID cannot be empty');
    });

    test('should reject whitespace-only userId', () => {
      // Act
      const result = walletValidation.validateBalanceRequest({ userId: '   ' });

      // Assert
      expect(result.isValid).toBe(false);
      expect(result.error).toBe('User ID cannot be empty');
    });

    test('should reject null userId', () => {
      // Act
      const result = walletValidation.validateBalanceRequest({ userId: null });

      // Assert
      expect(result.isValid).toBe(false);
      expect(result.error).toBe('User ID must be a string');
    });

    test('should reject boolean userId', () => {
      // Act
      const result = walletValidation.validateBalanceRequest({ userId: true });

      // Assert
      expect(result.isValid).toBe(false);
      expect(result.error).toBe('User ID must be a string');
    });

    test('should reject array userId', () => {
      // Act
      const result = walletValidation.validateBalanceRequest({ userId: ['test'] });

      // Assert
      expect(result.isValid).toBe(false);
      expect(result.error).toBe('User ID must be a string');
    });

    test('should reject object userId', () => {
      // Act
      const result = walletValidation.validateBalanceRequest({ userId: { id: 'test' } });

      // Assert
      expect(result.isValid).toBe(false);
      expect(result.error).toBe('User ID must be a string');
    });
  });

  describe('validateUserId', () => {
    test('should validate valid user ID', () => {
      // Act
      const result = walletValidation.validateUserId('test-user-123');

      // Assert
      expect(result.isValid).toBe(true);
    });

    test('should validate UUID format', () => {
      // Act
      const result = walletValidation.validateUserId('550e8400-e29b-41d4-a716-************');

      // Assert
      expect(result.isValid).toBe(true);
    });

    test('should validate alphanumeric with hyphens', () => {
      // Act
      const result = walletValidation.validateUserId('user-123-abc-def');

      // Assert
      expect(result.isValid).toBe(true);
    });

    test('should reject null userId', () => {
      // Act
      const result = walletValidation.validateUserId(null);

      // Assert
      expect(result.isValid).toBe(false);
      expect(result.error).toBe('User ID is required');
    });

    test('should reject undefined userId', () => {
      // Act
      const result = walletValidation.validateUserId(undefined);

      // Assert
      expect(result.isValid).toBe(false);
      expect(result.error).toBe('User ID is required');
    });

    test('should reject empty string userId', () => {
      // Act
      const result = walletValidation.validateUserId('');

      // Assert
      expect(result.isValid).toBe(false);
      expect(result.error).toBe('User ID is required');
    });

    test('should reject whitespace-only userId', () => {
      // Act
      const result = walletValidation.validateUserId('   ');

      // Assert
      expect(result.isValid).toBe(false);
      expect(result.error).toBe('User ID is required');
    });

    test('should reject non-string userId', () => {
      // Act
      const result = walletValidation.validateUserId(123);

      // Assert
      expect(result.isValid).toBe(false);
      expect(result.error).toBe('User ID must be a string');
    });

    test('should reject very long userId', () => {
      // Act
      const longUserId = 'a'.repeat(256);
      const result = walletValidation.validateUserId(longUserId);

      // Assert
      expect(result.isValid).toBe(false);
      expect(result.error).toBe('User ID is too long (max 255 characters)');
    });

    test('should accept maximum length userId', () => {
      // Act
      const maxLengthUserId = 'a'.repeat(255);
      const result = walletValidation.validateUserId(maxLengthUserId);

      // Assert
      expect(result.isValid).toBe(true);
    });
  });

  describe('validateTokenAmount', () => {
    test('should validate positive integer amount', () => {
      // Act
      const result = walletValidation.validateTokenAmount(100, 'test operation');

      // Assert
      expect(result.isValid).toBe(true);
    });

    test('should validate large integer amount', () => {
      // Act
      const result = walletValidation.validateTokenAmount(1000000, 'test operation');

      // Assert
      expect(result.isValid).toBe(true);
    });

    test('should reject undefined amount', () => {
      // Act
      const result = walletValidation.validateTokenAmount(undefined, 'test operation');

      // Assert
      expect(result.isValid).toBe(false);
      expect(result.error).toBe('Amount is required for test operation');
    });

    test('should reject null amount', () => {
      // Act
      const result = walletValidation.validateTokenAmount(null, 'test operation');

      // Assert
      expect(result.isValid).toBe(false);
      expect(result.error).toBe('Amount is required for test operation');
    });

    test('should reject zero amount', () => {
      // Act
      const result = walletValidation.validateTokenAmount(0, 'test operation');

      // Assert
      expect(result.isValid).toBe(false);
      expect(result.error).toBe('Amount must be greater than 0 for test operation');
    });

    test('should reject negative amount', () => {
      // Act
      const result = walletValidation.validateTokenAmount(-100, 'test operation');

      // Assert
      expect(result.isValid).toBe(false);
      expect(result.error).toBe('Amount must be greater than 0 for test operation');
    });

    test('should reject decimal amount (MVT must be integer)', () => {
      // Act
      const result = walletValidation.validateTokenAmount(100.5, 'test operation');

      // Assert
      expect(result.isValid).toBe(false);
      expect(result.error).toBe('Amount must be an integer for test operation');
    });

    test('should reject string amount', () => {
      // Act
      const result = walletValidation.validateTokenAmount('100', 'test operation');

      // Assert
      expect(result.isValid).toBe(false);
      expect(result.error).toBe('Amount must be a number for test operation');
    });

    test('should reject boolean amount', () => {
      // Act
      const result = walletValidation.validateTokenAmount(true, 'test operation');

      // Assert
      expect(result.isValid).toBe(false);
      expect(result.error).toBe('Amount must be a number for test operation');
    });

    test('should reject array amount', () => {
      // Act
      const result = walletValidation.validateTokenAmount([100], 'test operation');

      // Assert
      expect(result.isValid).toBe(false);
      expect(result.error).toBe('Amount must be a number for test operation');
    });

    test('should reject object amount', () => {
      // Act
      const result = walletValidation.validateTokenAmount({ amount: 100 }, 'test operation');

      // Assert
      expect(result.isValid).toBe(false);
      expect(result.error).toBe('Amount must be a number for test operation');
    });

    test('should reject NaN amount', () => {
      // Act
      const result = walletValidation.validateTokenAmount(NaN, 'test operation');

      // Assert
      expect(result.isValid).toBe(false);
      expect(result.error).toBe('Amount must be a number for test operation');
    });

    test('should reject Infinity amount', () => {
      // Act
      const result = walletValidation.validateTokenAmount(Infinity, 'test operation');

      // Assert
      expect(result.isValid).toBe(false);
      expect(result.error).toBe('Amount must be a number for test operation');
    });

    test('should use default operation name when not provided', () => {
      // Act
      const result = walletValidation.validateTokenAmount(null);

      // Assert
      expect(result.isValid).toBe(false);
      expect(result.error).toBe('Amount is required for operation');
    });
  });

  describe('Edge Cases and Boundary Values', () => {
    test('should handle very large valid amounts', () => {
      // Act
      const result = walletValidation.validateTokenAmount(Number.MAX_SAFE_INTEGER, 'test');

      // Assert
      expect(result.isValid).toBe(true);
    });

    test('should reject amounts larger than MAX_SAFE_INTEGER', () => {
      // Act
      const result = walletValidation.validateTokenAmount(Number.MAX_SAFE_INTEGER + 1, 'test');

      // Assert
      expect(result.isValid).toBe(false);
    });

    test('should handle minimum valid amount', () => {
      // Act
      const result = walletValidation.validateTokenAmount(1, 'test');

      // Assert
      expect(result.isValid).toBe(true);
    });

    test('should handle special number values', () => {
      // Test various special number cases
      expect(walletValidation.validateTokenAmount(-0, 'test').isValid).toBe(false);
      expect(walletValidation.validateTokenAmount(Number.NEGATIVE_INFINITY, 'test').isValid).toBe(false);
      expect(walletValidation.validateTokenAmount(Number.POSITIVE_INFINITY, 'test').isValid).toBe(false);
    });
  });
});
