{"name": "graphqlcustomfunction", "version": "2.0.0", "description": "Lambda function generated by Amplify", "main": "index.js", "license": "Apache-2.0", "dependencies": {"@apollo/client": "^3.8.8", "@aws-sdk/client-bedrock-runtime": "^3.731.1", "@aws-sdk/client-transcribe": "^3.760.0", "apollo-link-http": "^1.5.17", "aws-sdk": "^2.1245.0", "aws-serverless-express": "^3.3.5", "dotenv": "^16.4.7", "express-generator": "^4.16.1", "graphql": "^15.8.0", "node-fetch": "^2.6.1", "openai": "^4.28.0", "pino": "^8.17.2"}, "devDependencies": {"@types/aws-lambda": "^8.10.92"}}